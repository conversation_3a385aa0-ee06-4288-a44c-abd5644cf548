{"name": "trailbase-wasm", "description": "WASM Guest Runtime for custom JS/TS endpoints in TrailBase", "homepage": "https://trailbase.io", "version": "0.2.0", "license": "OSL-3.0", "type": "module", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./db": {"import": "./dist/db.js", "types": "./dist/db.d.ts"}, "./fs": {"import": "./dist/fs.js", "types": "./dist/fs.d.ts"}, "./http": {"import": "./dist/http.js", "types": "./dist/http.d.ts"}, "./job": {"import": "./dist/job.js", "types": "./dist/job.d.ts"}, "./kv": {"import": "./dist/kv.js", "types": "./dist/kv.d.ts"}}, "publishConfig": {"access": "public", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./db": {"import": "./dist/db.js", "types": "./dist/db.d.ts"}, "./fs": {"import": "./dist/fs.js", "types": "./dist/fs.d.ts"}, "./http": {"import": "./dist/http.js", "types": "./dist/http.d.ts"}, "./job": {"import": "./dist/job.js", "types": "./dist/job.d.ts"}, "./kv": {"import": "./dist/kv.js", "types": "./dist/kv.d.ts"}}}, "files": ["dist", "package.json", "wit"], "repository": {"type": "git", "url": "https://github.com/trailbaseio/trailbase.git", "directory": "guests/typescript"}, "scripts": {"build": "vite build", "check": "tsc --noEmit --skipLibCheck && vite build && eslint", "format": "prettier -w src tests", "generate:types": "jco types wit/ -o generated/types", "prepack": "rm -rf ./dist && pnpm build && test -e ./dist/index.js", "test": "vitest run"}, "devDependencies": {"@bytecodealliance/jco": "^1.15.0", "@eslint/js": "^9.37.0", "eslint": "^9.37.0", "prettier": "^3.6.2", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vite": "^7.1.9", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}}