{"name": "example-wasm-guest-js", "private": true, "version": "0.0.0", "scripts": {"build:wasm": "jco componentize dist/index.mjs -w node_modules/trailbase-wasm/wit -o dist/component.wasm", "build:wasm:aot": "jco componentize dist/index.mjs -w node_modules/trailbase-wasm/wit --aot -o dist/component.wasm", "build": "vite build && npm run build:wasm", "check": "eslint", "format": "prettier -w src"}, "dependencies": {"trailbase-wasm": "^0.2.0"}, "devDependencies": {"@bytecodealliance/jco": "^1.15.0", "@eslint/js": "^9.37.0", "eslint": "^9.37.0", "globals": "^16.4.0", "prettier": "^3.6.2", "vite": "^7.1.9"}}