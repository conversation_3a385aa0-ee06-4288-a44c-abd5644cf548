{"name": "trailbase-sync-example", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc -b && vite build", "check": "tsc --noEmit --skipLibCheck && eslint", "dev": "vite", "format": "prettier -w src", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/db": "^0.4.4", "@tanstack/query-core": "^5.90.2", "@tanstack/query-db-collection": "^0.2.25", "@tanstack/react-db": "^0.1.26", "@tanstack/trailbase-db-collection": "^0.1.26", "react": "^19.2.0", "react-dom": "^19.2.0", "tailwindcss": "^4.1.14", "trailbase": "^0.7.4"}, "devDependencies": {"@eslint/js": "^9.37.0", "@tailwindcss/vite": "^4.1.14", "@types/react": "^19.2.0", "@types/react-dom": "^19.2.0", "@vitejs/plugin-react": "^5.0.4", "eslint": "^9.37.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.23", "globals": "^16.4.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vite": "^7.1.9"}}