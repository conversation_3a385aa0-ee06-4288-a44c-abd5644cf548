{"name": "coffee-vector-search-guest", "version": "0.1.0", "keywords": [], "author": "", "main": "dist/index.js", "scripts": {"build:tsc": "vite build", "build:wasm": "jco componentize dist/index.mjs -w ../../../../guests/typescript/wit -o dist/component.wasm", "build:wasm:aot": "jco componentize  dist/index.mjs -w ../../../../guests/typescript/wit --aot -o dist/component.wasm", "build": "npm run build:tsc && npm run build:wasm", "format": "prettier -w src"}, "dependencies": {"trailbase-wasm": "^0.2.0"}, "devDependencies": {"@bytecodealliance/jco": "^1.15.0", "@types/node": "^24.7.0", "prettier": "^3.6.2", "typescript": "^5.9.3", "vite": "^7.1.9", "vitest": "^3.2.4"}}