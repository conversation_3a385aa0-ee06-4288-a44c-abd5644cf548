{"name": "trailbase-example-blog", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "check": "astro check && eslint", "format": "prettier -w src *.mjs", "types": "make --always-make types"}, "dependencies": {"@nanostores/persistent": "^1.1.0", "@nanostores/solid": "^1.1.1", "astro": "^5.14.1", "astro-icon": "^1.1.5", "nanostores": "^1.0.1", "solid-icons": "^1.1.0", "solid-js": "^1.9.9", "trailbase": "^0.7.4"}, "devDependencies": {"@astrojs/solid-js": "^5.1.1", "@eslint/js": "^9.37.0", "@iconify-json/tabler": "^1.2.23", "@tailwindcss/typography": "^0.5.19", "@tailwindcss/vite": "^4.1.14", "@types/dateformat": "^5.0.3", "eslint": "^9.37.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-better-tailwindcss": "^3.7.9", "eslint-plugin-solid": "^0.14.5", "globals": "^16.4.0", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "quicktype": "^23.2.6", "sharp": "^0.34.4", "tailwindcss": "^4.1.14", "typescript-eslint": "^8.46.0"}}