{"name": "examples_tutorial_scripts", "version": "0.0.1", "description": "", "type": "module", "scripts": {"build": "tsc", "format": "prettier -w src", "read": "node --loader ts-node/esm src/index.js", "fill": "node --loader ts-node/esm src/fill.js", "check": "tsc --noEmit --skipLibCheck && eslint"}, "devDependencies": {"@eslint/js": "^9.37.0", "@types/node": "^24.7.0", "eslint": "^9.37.0", "prettier": "^3.6.2", "quicktype": "^23.2.6", "ts-node": "^10.9.2", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0"}, "dependencies": {"csv-parse": "^5.6.0", "trailbase": "^0.7.4"}}