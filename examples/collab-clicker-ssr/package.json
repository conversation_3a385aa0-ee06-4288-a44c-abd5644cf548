{"name": "collab-clicker-ssr", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "node server", "build": "pnpm run build:client && pnpm run build:server", "build:client": "vite build --outDir dist/client", "build:server": "vite build --ssr src/entry-server.tsx --outDir dist/server", "format": "prettier -w *.{ts,js,tsx,jsx}", "check": "tsc --noEmit --skipLibCheck && eslint"}, "dependencies": {"solid-js": "^1.9.9", "trailbase": "^0.7.4"}, "devDependencies": {"@eslint/js": "^9.37.0", "@tailwindcss/vite": "^4.1.14", "@types/express": "^5.0.3", "@types/node": "^24.7.0", "compression": "^1.8.1", "eslint": "^9.37.0", "express": "^5.1.0", "globals": "^16.4.0", "prettier": "^3.6.2", "sirv": "^3.0.2", "tailwindcss": "^4.1.14", "typescript-eslint": "^8.46.0", "vite": "^7.1.9", "vite-plugin-solid": "^2.11.9"}}