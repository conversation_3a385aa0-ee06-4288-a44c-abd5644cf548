import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// TrailBase Admin API 客户端示例
/// 
/// 使用方法：
/// 1. 确保安装了 http 包：dart pub add http
/// 2. 修改服务器地址和认证信息
/// 3. 运行：dart run trailbase_admin_example.dart
class TrailBaseAdminClient {
  final String baseUrl;
  String? authToken;
  String? csrfToken;
  
  TrailBaseAdminClient(this.baseUrl);
  
  /// 登录获取管理员令牌
  Future<void> login(String email, String password) async {
    print('🔐 正在登录...');
    final response = await http.post(
      Uri.parse('$baseUrl/api/auth/v1/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': email,
        'password': password,
      }),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      authToken = data['auth_token'];
      csrfToken = data['csrf_token'];
      print('✅ 登录成功');
    } else {
      throw Exception('登录失败: ${response.body}');
    }
  }
  
  /// 获取认证头
  Map<String, String> get _authHeaders => {
    'Content-Type': 'application/json',
    if (authToken != null) 'Authorization': 'Bearer $authToken',
    if (csrfToken != null) 'CSRF-Token': csrfToken!,
  };
  
  /// 获取表列表
  Future<List<dynamic>> getTables() async {
    print('📋 获取表列表...');
    final response = await http.get(
      Uri.parse('$baseUrl/api/_admin/tables'),
      headers: _authHeaders,
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      print('✅ 成功获取表列表');
      return data['tables'] ?? [];
    } else {
      throw Exception('获取表列表失败: ${response.body}');
    }
  }
  
  /// 创建表
  Future<void> createTable({
    required String tableName,
    required List<Map<String, dynamic>> columns,
    bool strict = false,
  }) async {
    print('🔨 创建表: $tableName');
    
    final tableSchema = {
      'name': {
        'name': tableName,
        'database_schema': null,
      },
      'strict': strict,
      'columns': columns.map((col) => {
        'name': col['name'],
        'data_type': _convertDataType(col['type']),
        'options': _parseColumnOptions(col['constraints'] ?? ''),
      }).toList(),
      'foreign_keys': <Map<String, dynamic>>[],
      'unique': <Map<String, dynamic>>[],
      'checks': <Map<String, dynamic>>[],
      'virtual_table': false,
      'temporary': false,
    };
    
    final response = await http.post(
      Uri.parse('$baseUrl/api/_admin/table'),
      headers: _authHeaders,
      body: jsonEncode({
        'schema': tableSchema,
        'dry_run': false,
      }),
    );
    
    if (response.statusCode == 200) {
      print('✅ 表创建成功');
    } else {
      throw Exception('创建表失败: ${response.body}');
    }
  }
  
  /// 删除表
  Future<void> dropTable(String tableName) async {
    print('🗑️ 删除表: $tableName');
    
    final response = await http.delete(
      Uri.parse('$baseUrl/api/_admin/table'),
      headers: _authHeaders,
      body: jsonEncode({
        'name': tableName,
        'dry_run': false,
      }),
    );
    
    if (response.statusCode == 200) {
      print('✅ 表删除成功');
    } else {
      print('⚠️ 删除表失败: ${response.body}');
    }
  }
  
  /// 获取配置信息
  Future<Map<String, dynamic>> getConfig() async {
    print('⚙️ 获取当前配置...');
    
    final response = await http.get(
      Uri.parse('$baseUrl/api/_admin/config'),
      headers: {
        'Authorization': 'Bearer $authToken',
        if (csrfToken != null) 'CSRF-Token': csrfToken!,
      },
    );
    
    if (response.statusCode == 200) {
      print('✅ 成功获取配置');
      return {
        'config_data': response.bodyBytes,
        'content_type': response.headers['content-type'],
      };
    } else {
      throw Exception('获取配置失败: ${response.body}');
    }
  }
  
  /// 测试 Record API 端点
  Future<void> testRecordApi(String apiName) async {
    print('🧪 测试 Record API: $apiName');
    
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/records/v1/$apiName/schema'),
        headers: _authHeaders,
      );
      
      if (response.statusCode == 200) {
        print('✅ Record API 可用');
      } else {
        print('⚠️ Record API 不可用: ${response.statusCode}');
      }
    } catch (e) {
      print('⚠️ Record API 测试失败: $e');
    }
  }
  
  /// 数据类型转换
  String _convertDataType(String type) {
    switch (type.toUpperCase()) {
      case 'INTEGER': return 'Integer';
      case 'TEXT': return 'Text';
      case 'BLOB': return 'Blob';
      case 'REAL': return 'Real';
      case 'NUMERIC': return 'Numeric';
      default: return 'Text';
    }
  }
  
  /// 解析列选项
  List<dynamic> _parseColumnOptions(String constraints) {
    final options = <dynamic>[];
    
    if (constraints.contains('PRIMARY KEY')) {
      options.add({
        'Unique': {
          'is_primary': true,
          'conflict_clause': null,
        }
      });
    }
    
    if (constraints.contains('NOT NULL')) {
      options.add('NotNull');
    }
    
    final defaultMatch = RegExp(r'DEFAULT\s*\(([^)]+)\)').firstMatch(constraints);
    if (defaultMatch != null) {
      options.add({
        'Default': '(${defaultMatch.group(1)!})'
      });
    }
    
    return options;
  }
}

/// 主函数 - 演示完整的使用流程
void main() async {
  // 配置服务器信息
  final client = TrailBaseAdminClient('http://117.72.60.131:4000');
  
  try {
    // 1. 登录
    await client.login('<EMAIL>', 'lzm_0112333');
    
    // 2. 获取现有表列表
    final tables = await client.getTables();
    print('📊 当前表数量: ${tables.length}');
    
    // 3. 创建示例表
    final testTableName = 'demo_table_${DateTime.now().millisecondsSinceEpoch}';
    
    await client.createTable(
      tableName: testTableName,
      columns: [
        {
          'name': 'id',
          'type': 'INTEGER',
          'constraints': 'PRIMARY KEY',
        },
        {
          'name': 'name',
          'type': 'TEXT',
          'constraints': 'NOT NULL',
        },
        {
          'name': 'description',
          'type': 'TEXT',
          'constraints': '',
        },
      ],
    );
    
    // 4. 验证表创建
    final updatedTables = await client.getTables();
    print('📊 更新后表数量: ${updatedTables.length}');
    
    // 5. 获取配置信息
    final config = await client.getConfig();
    print('📄 配置数据大小: ${config['config_data'].length} bytes');
    
    // 6. 测试现有的 Record API（如果有的话）
    print('🔍 测试现有 Record APIs...');
    for (final table in tables.take(3)) { // 只测试前3个表
      final tableName = table['name'];
      if (tableName is String && !tableName.startsWith('_')) {
        await client.testRecordApi(tableName);
      }
    }
    
    // 7. 清理：删除测试表
    await client.dropTable(testTableName);
    
    print('');
    print('🎉 演示完成！');
    print('');
    print('📋 功能演示总结:');
    print('✅ 管理员登录认证');
    print('✅ 获取数据库表列表');
    print('✅ 创建新表');
    print('✅ 删除表');
    print('✅ 获取系统配置');
    print('✅ 测试 Record API 端点');
    print('');
    print('💡 提示：要配置 Record APIs，需要修改系统配置');
    print('   配置格式请参考 TrailBase_Admin_API_Guide.md');
    
  } catch (e, stackTrace) {
    print('❌ 演示失败: $e');
    print('堆栈跟踪: $stackTrace');
    exit(1);
  }
}
