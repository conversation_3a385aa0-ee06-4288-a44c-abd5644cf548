# TrailBase Admin REST API 使用指南

本文档详细说明如何使用管理员账号通过 REST API 管理 TrailBase 的表结构和 Record APIs 配置。

## 目录

- [认证](#认证)
- [表管理 API](#表管理-api)
- [Record APIs 配置](#record-apis-配置)
- [完整示例](#完整示例)
- [注意事项](#注意事项)

## 认证

所有 Admin API 都需要管理员权限和 CSRF 保护。首先需要通过认证 API 获取访问令牌和 CSRF token：

### 登录获取令牌

**端点**: `POST /api/auth/v1/login`

```dart
Future<void> login(String email, String password) async {
  final response = await http.post(
    Uri.parse('$baseUrl/api/auth/v1/login'),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({
      'email': email,
      'password': password,
    }),
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    authToken = data['auth_token'];
    csrfToken = data['csrf_token'];
    print('✅ 登录成功，获取到令牌和CSRF token');
  } else {
    throw Exception('登录失败: ${response.body}');
  }
}
```

### 认证头设置

所有后续的 Admin API 请求都需要包含以下头部：

```dart
Map<String, String> get _authHeaders => {
  'Content-Type': 'application/json',
  if (authToken != null) 'Authorization': 'Bearer $authToken',
  if (csrfToken != null) 'CSRF-Token': csrfToken!,
};
```

## 表管理 API

### 1. 获取表列表

**端点**: `GET /api/_admin/tables`

```dart
Future<List<dynamic>> getTables() async {
  final response = await http.get(
    Uri.parse('$baseUrl/api/_admin/tables'),
    headers: _authHeaders,
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return data['tables'] ?? [];
  } else {
    throw Exception('获取表列表失败: ${response.body}');
  }
}
```

### 2. 创建表

**端点**: `POST /api/_admin/table`

创建表需要提供完整的表 schema 对象：

```dart
Future<void> createTable({
  required String tableName,
  required List<Map<String, dynamic>> columns,
  bool strict = false,
}) async {
  // 构建表 schema 对象
  final tableSchema = {
    'name': {
      'name': tableName,
      'database_schema': null,
    },
    'strict': strict,
    'columns': columns.map((col) => {
      'name': col['name'],
      'data_type': _convertDataType(col['type']),
      'options': _parseColumnOptions(col['constraints'] ?? ''),
    }).toList(),
    'foreign_keys': <Map<String, dynamic>>[],
    'unique': <Map<String, dynamic>>[],
    'checks': <Map<String, dynamic>>[],
    'virtual_table': false,
    'temporary': false,
  };
  
  final requestBody = {
    'schema': tableSchema,
    'dry_run': false,
  };
  
  final response = await http.post(
    Uri.parse('$baseUrl/api/_admin/table'),
    headers: _authHeaders,
    body: jsonEncode(requestBody),
  );
  
  if (response.statusCode == 200) {
    print('✅ 表创建成功');
  } else {
    throw Exception('创建表失败: ${response.body}');
  }
}

// 数据类型转换
String _convertDataType(String type) {
  switch (type.toUpperCase()) {
    case 'INTEGER': return 'Integer';
    case 'TEXT': return 'Text';
    case 'BLOB': return 'Blob';
    case 'REAL': return 'Real';
    case 'NUMERIC': return 'Numeric';
    default: return 'Text';
  }
}

// 列选项解析
List<dynamic> _parseColumnOptions(String constraints) {
  final options = <dynamic>[];
  
  if (constraints.contains('PRIMARY KEY')) {
    options.add({
      'Unique': {
        'is_primary': true,
        'conflict_clause': null,
      }
    });
  }
  
  if (constraints.contains('NOT NULL')) {
    options.add('NotNull');
  }
  
  // 处理 DEFAULT 值
  final defaultMatch = RegExp(r'DEFAULT\s*\(([^)]+)\)').firstMatch(constraints);
  if (defaultMatch != null) {
    options.add({
      'Default': '(${defaultMatch.group(1)!})'
    });
  }
  
  return options;
}
```

### 3. 删除表

**端点**: `DELETE /api/_admin/table`

```dart
Future<void> dropTable(String tableName) async {
  final response = await http.delete(
    Uri.parse('$baseUrl/api/_admin/table'),
    headers: _authHeaders,
    body: jsonEncode({
      'name': tableName,
      'dry_run': false,
    }),
  );
  
  if (response.statusCode == 200) {
    print('✅ 表删除成功');
  } else {
    throw Exception('删除表失败: ${response.body}');
  }
}
```

## Record APIs 配置

### 1. 获取当前配置

**端点**: `GET /api/_admin/config`

```dart
Future<Map<String, dynamic>> getConfig() async {
  final response = await http.get(
    Uri.parse('$baseUrl/api/_admin/config'),
    headers: {
      'Authorization': 'Bearer $authToken',
      if (csrfToken != null) 'CSRF-Token': csrfToken!,
    },
  );
  
  if (response.statusCode == 200) {
    // 返回的是 protobuf 格式数据
    return {
      'config_data': response.bodyBytes,
      'content_type': response.headers['content-type'],
    };
  } else {
    throw Exception('获取配置失败: ${response.body}');
  }
}
```

### 2. 配置 Record API

**端点**: `POST /api/_admin/config`

由于配置 API 使用 Protocol Buffers 格式，实际的配置更新需要：

1. 获取当前配置
2. 解码 protobuf 数据
3. 修改 `record_apis` 数组
4. 重新编码并发送

Record API 配置结构示例：

```dart
final recordApiConfig = {
  'name': 'articles',                    // API 名称
  'table_name': 'articles',              // 对应的表名
  'acl_world': [2],                      // 公开权限：READ (2)
  'acl_authenticated': [1, 2, 4, 8],     // 认证用户权限：CREATE(1), READ(2), UPDATE(4), DELETE(8)
  'enable_subscriptions': true,          // 启用实时订阅
  'create_access_rule': '_REQ_.author = _USER_.id',  // 创建访问规则
  'read_access_rule': null,              // 读取访问规则
  'update_access_rule': '_ROW_.author = _USER_.id',  // 更新访问规则
  'delete_access_rule': '_ROW_.author = _USER_.id',  // 删除访问规则
};
```

权限标志：
- `CREATE = 1`
- `READ = 2`
- `UPDATE = 4`
- `DELETE = 8`
- `SCHEMA = 16`

## 完整示例

以下是一个完整的示例，演示如何创建表并测试相关功能：

```dart
void main() async {
  final client = TrailBaseAdminClient('http://*************:4000');
  
  try {
    // 1. 登录
    await client.login('<EMAIL>', 'lzm_0112333');
    
    // 2. 获取现有表列表
    final tables = await client.getTables();
    print('📊 当前表数量: ${tables.length}');
    
    // 3. 创建测试表
    final testTableName = 'test_articles_${DateTime.now().millisecondsSinceEpoch}';
    
    await client.createTable(
      tableName: testTableName,
      strict: false,
      columns: [
        {
          'name': 'id',
          'type': 'INTEGER',
          'constraints': 'PRIMARY KEY',
        },
        {
          'name': 'title',
          'type': 'TEXT',
          'constraints': 'NOT NULL',
        },
        {
          'name': 'content',
          'type': 'TEXT',
          'constraints': '',
        },
      ],
    );
    
    // 4. 验证表创建
    final updatedTables = await client.getTables();
    final createdTable = updatedTables.firstWhere(
      (table) => table['name'] == testTableName,
      orElse: () => null,
    );
    
    if (createdTable != null) {
      print('✅ 表创建验证成功');
    }
    
    // 5. 获取配置信息
    final config = await client.getConfig();
    print('配置数据大小: ${config['config_data'].length} bytes');
    
    // 6. 清理：删除测试表
    await client.dropTable(testTableName);
    
    print('🎉 所有操作完成！');
    
  } catch (e) {
    print('❌ 操作失败: $e');
  }
}
```

## 注意事项

1. **Protocol Buffers**: 配置 API 使用 protobuf 格式，需要相应的编解码库
2. **CSRF 保护**: 所有 Admin API 都需要 `CSRF-Token` 头部
3. **权限控制**: 需要管理员权限才能访问 Admin API
4. **数据类型**: 表创建时需要使用正确的数据类型格式
5. **访问规则**: Record API 的访问规则使用 SQL 表达式，支持特殊变量：
   - `_USER_`: 当前用户信息
   - `_ROW_`: 当前记录数据
   - `_REQ_`: 请求数据
6. **实时生效**: 配置更改会立即生效，无需重启服务

## API 端点总结

| 功能 | 方法 | 端点 | 说明 |
|------|------|------|------|
| 登录 | POST | `/api/auth/v1/login` | 获取认证令牌 |
| 获取表列表 | GET | `/api/_admin/tables` | 列出所有表和视图 |
| 创建表 | POST | `/api/_admin/table` | 创建新表 |
| 删除表 | DELETE | `/api/_admin/table` | 删除表 |
| 获取配置 | GET | `/api/_admin/config` | 获取当前配置 |
| 更新配置 | POST | `/api/_admin/config` | 更新配置（包括 Record APIs） |

配置完成后，Record API 将在以下端点可用：
- `GET /api/records/v1/{api_name}` - 列表
- `POST /api/records/v1/{api_name}` - 创建
- `GET /api/records/v1/{api_name}/{id}` - 读取
- `PATCH /api/records/v1/{api_name}/{id}` - 更新
- `DELETE /api/records/v1/{api_name}/{id}` - 删除
- `GET /api/records/v1/{api_name}/schema` - 获取 Schema

## 测试结果

基于实际测试（服务器：*************:4000），以下功能已验证可用：

✅ **管理员登录** - 成功获取 auth_token 和 csrf_token
✅ **获取表列表** - 成功列出所有表和视图
✅ **创建表** - 成功创建包含主键和约束的表
✅ **删除表** - 成功删除指定表
✅ **获取配置信息** - 成功获取 protobuf 格式的配置数据
✅ **Record API 配置演示** - 展示了配置结构和流程

## 依赖包

在 `pubspec.yaml` 中添加：

```yaml
dependencies:
  http: ^1.1.0
  # 如果需要 protobuf 支持
  protobuf: ^3.1.0
```

## 错误处理

常见错误及解决方案：

1. **401 Unauthorized** - 检查认证令牌是否有效
2. **400 Bad Request: admin APIs require csrf header** - 确保包含 `CSRF-Token` 头部
3. **422 Unprocessable Entity** - 检查请求体格式是否正确
4. **500 Internal Server Error** - 检查 SQL 语法或表结构定义

## 最佳实践

1. **安全性**: 始终使用 HTTPS 连接生产环境
2. **错误处理**: 实现完整的错误处理和重试机制
3. **配置管理**: 使用版本控制管理配置变更
4. **测试**: 在开发环境充分测试后再应用到生产环境
5. **监控**: 监控 API 调用和数据库变更

这个 API 设计使得 TrailBase 可以完全通过编程方式进行管理，非常适合自动化部署和配置管理场景。
