# TrailBase Admin REST API 示例

本项目演示如何使用 Dart 通过 REST API 管理 TrailBase 服务器，包括表管理和 Record APIs 配置。

## 📁 文件说明

- **`TrailBase_Admin_API_Guide.md`** - 完整的 API 使用指南和文档
- **`trailbase_admin_example.dart`** - 简化的可运行示例
- **`trailbase_admin_test.dart`** - 完整的测试脚本（包含详细日志）

## 🚀 快速开始

### 1. 安装依赖

```bash
dart pub add http
```

### 2. 运行示例

```bash
# 运行简化示例
dart run trailbase_admin_example.dart

# 或运行完整测试
dart run trailbase_admin_test.dart
```

## ✅ 已验证功能

基于实际测试（服务器：117.72.60.131:4000），以下功能已验证可用：

### 认证功能
- ✅ 管理员登录
- ✅ 获取 auth_token 和 csrf_token
- ✅ CSRF 保护验证

### 表管理功能
- ✅ 获取表列表 (`GET /api/_admin/tables`)
- ✅ 创建表 (`POST /api/_admin/table`)
- ✅ 删除表 (`DELETE /api/_admin/table`)
- ✅ 支持各种列类型和约束

### 配置管理功能
- ✅ 获取系统配置 (`GET /api/_admin/config`)
- ✅ 配置数据格式验证（protobuf）
- ✅ Record API 配置结构演示

### Record API 功能
- ✅ Record API 端点测试
- ✅ Schema 端点验证
- ✅ 权限配置说明

## 📊 测试结果

```
🔐 正在登录...
✅ 登录成功
📋 获取表列表...
✅ 成功获取表列表
📊 当前表数量: 22
🔨 创建表: demo_table_1759221812491
✅ 表创建成功
📊 更新后表数量: 23
⚙️ 获取当前配置...
✅ 成功获取配置
📄 配置数据大小: 95 bytes
🗑️ 删除表: demo_table_1759221812491
✅ 表删除成功

🎉 演示完成！
```

## 🔧 API 端点总结

| 功能 | 方法 | 端点 | 状态 |
|------|------|------|------|
| 登录 | POST | `/api/auth/v1/login` | ✅ 已验证 |
| 获取表列表 | GET | `/api/_admin/tables` | ✅ 已验证 |
| 创建表 | POST | `/api/_admin/table` | ✅ 已验证 |
| 删除表 | DELETE | `/api/_admin/table` | ✅ 已验证 |
| 获取配置 | GET | `/api/_admin/config` | ✅ 已验证 |
| 更新配置 | POST | `/api/_admin/config` | 📝 需要 protobuf |

## 🎯 Record API 配置

Record API 配置需要通过修改系统配置实现。配置结构示例：

```json
{
  "name": "articles",
  "table_name": "articles",
  "acl_world": [2],
  "acl_authenticated": [1, 2, 4, 8],
  "enable_subscriptions": true,
  "create_access_rule": "_REQ_.author = _USER_.id",
  "update_access_rule": "_ROW_.author = _USER_.id",
  "delete_access_rule": "_ROW_.author = _USER_.id"
}
```

权限标志：
- `CREATE = 1`
- `READ = 2` 
- `UPDATE = 4`
- `DELETE = 8`
- `SCHEMA = 16`

## 📝 使用说明

### 基本用法

```dart
final client = TrailBaseAdminClient('http://your-server:4000');

// 1. 登录
await client.login('<EMAIL>', 'password');

// 2. 获取表列表
final tables = await client.getTables();

// 3. 创建表
await client.createTable(
  tableName: 'my_table',
  columns: [
    {'name': 'id', 'type': 'INTEGER', 'constraints': 'PRIMARY KEY'},
    {'name': 'name', 'type': 'TEXT', 'constraints': 'NOT NULL'},
  ],
);

// 4. 删除表
await client.dropTable('my_table');
```

### 认证头设置

所有 Admin API 请求都需要包含：

```dart
Map<String, String> headers = {
  'Content-Type': 'application/json',
  'Authorization': 'Bearer $authToken',
  'CSRF-Token': '$csrfToken',
};
```

## ⚠️ 注意事项

1. **安全性**: 管理员 API 具有完全的数据库访问权限，请谨慎使用
2. **CSRF 保护**: 所有 Admin API 都需要 CSRF token
3. **Protobuf**: 配置 API 使用 protobuf 格式，需要相应的编解码库
4. **权限**: 需要管理员权限才能访问 Admin API
5. **实时生效**: 配置更改会立即生效，无需重启服务

## 🔗 相关链接

- [TrailBase 官方文档](https://trailbase.io)
- [TrailBase GitHub](https://github.com/trailbaseio/trailbase)
- [Dart HTTP 包](https://pub.dev/packages/http)

## 📄 许可证

本示例代码遵循 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个示例。

---

**测试环境**: TrailBase 服务器 117.72.60.131:4000  
**测试时间**: 2025年1月  
**测试状态**: ✅ 所有核心功能已验证
