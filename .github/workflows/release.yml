name: release

on:
  push:
    tags:
      - "v*.*.*"

jobs:
  changelog:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Get release notes from changelog
        id: release_notes
        run: |
          export VERSION=$(echo ${{  github.ref_name }} | sed 's/^v//')
          echo "VERSION = ${VERSION}"
          awk "/## v${VERSION}.*/{include=1; next} /## v.*/{include=0} include && NF" CHANGELOG.md > ./CHANGELOG-release.md
          cat ./CHANGELOG-release.md

      - name: Release Change log
        uses: softprops/action-gh-release@v2
        with:
          generate_release_notes: true
          body_path: ./CHANGELOG-release.md

  release-linux-glibc:
    needs: changelog
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Install Dependencies
        run: |
          sudo apt-get update && \
            sudo apt-get install -y --no-install-recommends curl libssl-dev pkg-config libclang-dev protobuf-compiler libprotobuf-dev zip
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: PNPM install
        run: |
          pnpm install
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: 1.90.0
          target: x86_64-unknown-linux-gnu
          default: true
      - name: Rust Build
        # NOTE: cargo links different binaries whether in the crate or workspace root :/.
        run: |
          cd crates/cli &&  \
          CARGO_PROFILE_RELEASE_LTO=fat CARGO_PROFILE_RELEASE_CODEGEN_UNITS=1 PNPM_OFFLINE=TRUE \
            cargo build --target x86_64-unknown-linux-gnu --release --bin trail && \
          cd ../.. && \
          zip -r -j trailbase_${{  github.ref_name }}_x86_64_linux_glibc.zip target/x86_64-unknown-linux-gnu/release/trail CHANGELOG.md LICENSE

      - name: Release binaries
        uses: softprops/action-gh-release@v2
        with:
          fail_on_unmatched_files: true
          files: trailbase_${{  github.ref_name }}_x86_64_linux_glibc.zip

  release-linux-static:
    needs: changelog
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Install Dependencies
        run: |
          sudo apt-get update && \
            sudo apt-get install -y --no-install-recommends curl libssl-dev pkg-config libclang-dev protobuf-compiler libprotobuf-dev zip musl-tools
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: PNPM install
        run: |
          pnpm install
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: 1.90.0
          target: x86_64-unknown-linux-musl
          default: true
      - name: Rust Build
        run: |
          cd crates/cli &&  \
          CARGO_PROFILE_RELEASE_LTO=fat CARGO_PROFILE_RELEASE_CODEGEN_UNITS=1 PNPM_OFFLINE=TRUE \
            cargo build --target x86_64-unknown-linux-musl --features=vendor-ssl --release --bin trail && \
          cd ../.. && \
          zip -r -j trailbase_${{  github.ref_name }}_x86_64_linux.zip target/x86_64-unknown-linux-musl/release/trail CHANGELOG.md LICENSE

      - name: Release binaries
        uses: softprops/action-gh-release@v2
        with:
          fail_on_unmatched_files: true
          files: trailbase_${{  github.ref_name }}_x86_64_linux.zip

  release-linux-auth-ui:
    needs: changelog
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Install Dependencies
        run: |
          sudo apt-get update && \
            sudo apt-get install -y --no-install-recommends curl libssl-dev pkg-config libclang-dev protobuf-compiler libprotobuf-dev zip
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: PNPM install
        run: |
          pnpm install
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: 1.90.0
          target: wasm32-wasip2
          default: true
      - name: Rust Build
        run: |
          cd crates/auth-ui && \
          make && \
          cd ../.. && \
          zip -r -j trailbase_${{  github.ref_name }}_wasm_auth_ui.zip target/wasm32-wasip2/release/auth_ui_component.wasm CHANGELOG.md LICENSE

      - name: Release binaries
        uses: softprops/action-gh-release@v2
        with:
          fail_on_unmatched_files: true
          files: trailbase_${{  github.ref_name }}_wasm_auth_ui.zip

  release-aarch64-apple-darwin:
    needs: changelog
    runs-on: macos-14
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Install Dependencies
        run: |
            brew update && brew install protobuf sqlite

      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: PNPM install
        run: |
          pnpm install
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: 1.90.0
          target: aarch64-apple-darwin
          default: true
      - name: Rust Build
        run: |
          CARGO_PROFILE_RELEASE_LTO=fat CARGO_PROFILE_RELEASE_CODEGEN_UNITS=1 PNPM_OFFLINE=TRUE \
            cargo build --target aarch64-apple-darwin --release --bin trail && \
          zip -r -j trailbase_${{  github.ref_name }}_arm64_apple_darwin.zip target/aarch64-apple-darwin/release/trail CHANGELOG.md LICENSE

      - name: Release binaries
        uses: softprops/action-gh-release@v2
        with:
          fail_on_unmatched_files: true
          files: trailbase_${{  github.ref_name }}_arm64_apple_darwin.zip

  release-x86_64-apple-darwin:
    needs: changelog
    runs-on: macos-14
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Install Dependencies
        run: |
            brew update && brew install protobuf sqlite

      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: PNPM install
        run: |
          pnpm install
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: 1.90.0
          target: x86_64-apple-darwin
          default: true
      - name: Rust Build
        run: |
          CARGO_PROFILE_RELEASE_LTO=fat CARGO_PROFILE_RELEASE_CODEGEN_UNITS=1 PNPM_OFFLINE=TRUE \
            cargo build --target x86_64-apple-darwin --release --bin trail && \
          zip -r -j trailbase_${{  github.ref_name }}_x86_64_apple_darwin.zip target/x86_64-apple-darwin/release/trail CHANGELOG.md LICENSE

      - name: Release binaries
        uses: softprops/action-gh-release@v2
        with:
          fail_on_unmatched_files: true
          files: trailbase_${{  github.ref_name }}_x86_64_apple_darwin.zip

  release-windows:
    needs: changelog
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - uses: MinoruSekine/setup-scoop@v4.0.1
        with:
          buckets: extras
          apps: nodejs pnpm protobuf zip
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: 1.90.0
          target: x86_64-pc-windows-msvc
          default: true
      - name: Rust Build
        shell: bash
        run: |
          pnpm install && \
          CARGO_PROFILE_RELEASE_LTO=fat CARGO_PROFILE_RELEASE_CODEGEN_UNITS=1 PNPM_OFFLINE=TRUE \
            cargo build --target x86_64-pc-windows-msvc --release --bin trail && \
          zip -r -j trailbase_${{  github.ref_name }}_x86_64_windows.zip target/x86_64-pc-windows-msvc/release/trail.exe CHANGELOG.md LICENSE

      - name: Release binaries
        uses: softprops/action-gh-release@v2
        with:
          fail_on_unmatched_files: true
          files: trailbase_${{  github.ref_name }}_x86_64_windows.zip
