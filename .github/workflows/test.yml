name: test

on:
  pull_request:
  push:
    branches:
      - main
      - dev

jobs:

  test:
    runs-on: ubuntu-latest
    steps:
      - run: df -h
      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: false

          # all of these default to true, but feel free to set to
          # "false" if necessary for your workflow
          android: true
          dotnet: false
          haskell: true
          large-packages: false
          docker-images: true
          swap-storage: true
      - run: df -h
      - uses: actions/checkout@v3
        with:
          submodules: 'true'
      - name: Install Dependencies
        run: |
          sudo apt-get update && \
            sudo apt-get install -y --no-install-recommends curl libssl-dev pkg-config libclang-dev protobuf-compiler libprotobuf-dev
      - uses: actions/setup-python@v5
        with:
          python-version: '3.13'
      - name: Install Poetry and Python Dependencies
        run: |
          pipx install --python python3.13 "poetry==2.1.3" && poetry -C client/python install
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: PNPM install
        run: |
          pnpm i
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version: 3.35.4
      - name: Setup .NET SDK
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.x'
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: 1.90.0
          components: rustfmt, clippy
          default: true
      # - name: Setup Go
      #   uses: actions/setup-go@v5
      #   with:
      #     go-version: "1.24.x"
      # Swift 6.1 is preinstalled and swift-action sporadically fails on
      # gpg issues: https://github.com/swift-actions/setup-swift/issues/694
      # - uses: swift-actions/setup-swift@v2
      #   with:
      #     swift-version: "6.1"
      - uses: pre-commit/action@v3.0.1
        with:
          extra_args: "--all-files --verbose"
      - run: df -h
