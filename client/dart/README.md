# TrailBase Client

TrailBase is a [blazingly](https://trailbase.io/reference/benchmarks/) fast,
open-source application server with type-safe APIs, built-in WebAssembly runtime,
realtime, auth, and admin UI built on Rust, SQLite & Wasmtime.

For more context, documentation, and an online demo, check out the website:
[trailbase.io](https://trailbase.io).

This is the first-party client for hooking up your Flutter or Dart applications
with TrailBase.
While we're working on better documentation, an example Flutter app can be
found in the repository under:
[`/examples/blog/flutter`](https://github.com/trailbaseio/trailbase/tree/main/examples/blog/flutter).
