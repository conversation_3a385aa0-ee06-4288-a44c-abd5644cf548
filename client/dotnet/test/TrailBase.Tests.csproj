<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <PackageId>TrailBase.Tests</PackageId>
    <Version>0.0.1</Version>
    <Authors>TrailBase authors</Authors>
    <PackageLicenseExpression>OSL-3.0</PackageLicenseExpression>
    <PackageProjectUrl>https://trailbase.io</PackageProjectUrl>
    <RepositoryUrl>https://github.com/trailbaseio/trailbase</RepositoryUrl>

    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>

    <IsPackable>true</IsPackable>
    <IsAotCompatible>true</IsAotCompatible>
    <!-- <JsonSerializerIsReflectionEnabledByDefault>false</JsonSerializerIsReflectionEnabledByDefault> -->
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../trailbase\TrailBase.csproj" />

    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

</Project>
