{"$schema": "https://raw.githubusercontent.com/dotnet/docfx/main/schemas/docfx.schema.json", "metadata": [{"src": [{"src": "../trailbase", "files": ["**/*.c<PERSON><PERSON>j"], "exclude": ["**/bin/**", "**/obj/**"]}], "dest": "api"}], "build": {"content": [{"files": ["**/*.{md,yml}"], "exclude": ["_site/**"]}], "resource": [{"files": ["images/**"]}], "output": "_site", "template": ["default", "modern"], "globalMetadata": {"_appName": "TrailBase", "_appTitle": "TrailBase", "_appLogoPath": "images/logo_48.svg", "_appFaviconPath": "images/favicon.svg", "_enableSearch": true, "pdf": false}}}