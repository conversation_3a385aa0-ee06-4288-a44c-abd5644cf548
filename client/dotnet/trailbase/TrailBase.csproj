<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <PackageId>TrailBase</PackageId>
    <Version>0.5.0</Version>
    <Authors>TrailBase authors</Authors>
    <PackageReadmeFile>./README.md</PackageReadmeFile>
    <PackageLicenseExpression>OSL-3.0</PackageLicenseExpression>
    <PackageProjectUrl>https://trailbase.io</PackageProjectUrl>
    <RepositoryUrl>https://github.com/trailbaseio/trailbase</RepositoryUrl>

    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>

    <IsPackable>true</IsPackable>
    <IsAotCompatible>true</IsAotCompatible>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
  </PropertyGroup>

  <ItemGroup>
    <None Include="README.md" Pack="true" PackagePath="\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
  </ItemGroup>

</Project>
