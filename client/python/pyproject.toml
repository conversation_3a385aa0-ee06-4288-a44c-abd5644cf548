[tool.poetry]
name = "trailbase"
version = "0.4.0"
description = "TrailBase client for Python"
authors = ["TrailBase <<EMAIL>>"]
repository = "https://github.com/trailbaseio/trailbase"
homepage = "https://trailbase.io"
license = "OSL-3.0"
readme = "README.md"
requires-poetry = ">=2.0"

[tool.poetry.dependencies]
python = "^3.13"
httpx = "^0.27.2"
pyjwt = "^2.10.0"
cryptography = "^43.0.3"

[tool.poetry.group.dev.dependencies]
black = "^24.10.0"
pyright = "^1.1.389"
pytest = "^8.3.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 108

[tool.pyright]
venvPath = "."
venv = ".venv"
typeCheckingMode = "strict"
useLibraryCodeForTypes = true
verboseOutput = true
reportMissingTypeStubs = "warning"
