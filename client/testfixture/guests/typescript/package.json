{"name": "wasm-ts-guest-testfixture", "private": true, "version": "0.0.0", "scripts": {"build:tsc": "vite build", "build:wasm": "jco componentize dist/index.mjs -w ../../../../guests/typescript/wit -o dist/wasm_ts_guest_testfixture.wasm", "build:wasm:aot": "jco componentize  dist/index.mjs -w ../../../../guests/typescript/wit --aot -o dist/wasm_ts_guest_testfixture.wasm", "build": "npm run build:tsc && npm run build:wasm", "check": "tsc --noEmit --skipLibCheck && eslint", "deploy": "npm run build && rm -rf ../../wasm/* && cp dist/wasm_ts_guest_testfixture.wasm ../../wasm/", "format": "prettier -w src"}, "dependencies": {"trailbase-wasm": "workspace:*"}, "devDependencies": {"@bytecodealliance/jco": "^1.15.0", "@eslint/js": "^9.37.0", "@types/node": "^24.7.0", "eslint": "^9.37.0", "prettier": "^3.6.2", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vite": "^7.1.9"}}