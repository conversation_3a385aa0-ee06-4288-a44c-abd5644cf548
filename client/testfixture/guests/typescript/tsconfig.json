{
  "compilerOptions": {
    "target": "es2022",                          /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
    "module": "es2022",                          /* Specify what module code is generated. */
    "moduleResolution": "bundler",
    "paths": {},
    "outDir": "./dist/",                         /* Specify an output folder for all emitted files. */
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,    /* Ensure that casing is correct in imports. */
    "strict": true,                              /* Enable all strict type-checking options. */
    "skipLibCheck": true                         /* Skip type checking all .d.ts files. */
  }
}
