# Auto-generated config.Config textproto
email {}
server {
  application_name: "TrailBase"
  logs_retention_sec: 604800
  enable_record_transactions: true
}
auth {
  oauth_providers: [{
    key: "oidc0"
    value {
      client_id: "invalid_client_id"
      client_secret: "<REDACTED>"
      provider_id: OIDC0
      display_name: "My OIDC"
      auth_url: "http://localhost:9088/authorize"
      token_url: "http://localhost:9088/token"
      user_api_url: "http://localhost:9088/userinfo"
    }
  }, {
    key: "discord"
    value {
      client_id: "invalid_discord_id"
      client_secret: "<REDACTED>"
      provider_id: DISCORD
    }
  }]
}
jobs {}
record_apis: [{
  name: "simple_strict_table"
  table_name: "simple_strict_table"
  acl_authenticated: [CREATE, READ, UPDATE, DELETE]
  enable_subscriptions: true
}, {
  name: "simple_complete_view"
  table_name: "simple_complete_view"
  acl_authenticated: [CREATE, READ, UPDATE, DELETE]
}, {
  name: "simple_subset_view"
  table_name: "simple_subset_view"
  acl_authenticated: [CREATE, READ, UPDATE, DELETE]
}, {
  name: "movies"
  table_name: "movies"
  acl_world: [READ]
  acl_authenticated: [CREATE, READ, UPDATE, DELETE]
}, {
  name: "comment"
  table_name: "comment"
  acl_world: [READ]
  expand: ["author", "post"]
}, {
  name: "file_upload_table"
  table_name: "file_upload_table"
  acl_world: [CREATE, READ, DELETE]
  acl_authenticated: [CREATE, READ, UPDATE, DELETE]
}]
schemas: [{
  name: "simple_schema"
  schema: "{  \"type\": \"object\",  \"properties\": {    \"name\": { \"type\": \"string\" },    \"obj\": { \"type\": \"object\" }  },  \"required\": [\"name\"]}"
}]
