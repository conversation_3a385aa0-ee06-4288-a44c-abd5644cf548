[package]
name = "trailbase"
version = "0.2.0"
edition = "2024"
license = "OSL-3.0"
rust-version = "1.88"
description = "Package to use TrailBase as a framework"
homepage = "https://trailbase.io"
repository = "https://github.com/trailbaseio/trailbase"
readme = "../../README.md"
exclude = [
  "benches/",
  "tests/",
  "**/node_modules/",
  "**/dist/",
]
autobenches = false

[lib]
doctest = false

[[bench]]
name = "benchmark"
harness = false

[features]
default = ["wasm"]
v8 = ["dep:trailbase-js"]
wasm = ["dep:trailbase-wasm-runtime-host"]

[dependencies]
aes-gcm = { version = "0.10.3", default-features = false, features = ["aes", "alloc", "getrandom", "rand_core"] }
argon2 = { version = "^0.5.3", default-features = false, features = ["alloc", "password-hash"] }
askama = { workspace = true }
async-channel = "2.3.1"
async-trait = "0.1.80"
axum = { workspace = true }
base64 = { version = "0.22.1", default-features = false, features = ["alloc"] }
bytes = { version = "1.8.0", features = ["serde"] }
chrono = "^0.4.38"
client-ip = "0.1.1"
cron = "0.15.0"
ed25519-dalek = { version = "2.1.1", features = ["pkcs8", "pem", "rand_core"] }
fallible-iterator = "0.3.0"
form_urlencoded = "1.2.1"
futures-util = { version = "0.3", default-features = false, features = ["alloc"] }
http-body-util = "0.1.3"
hyper = "1.6.0"
hyper-util = "0.1.7"
indoc = "2.0.5"
itertools = "0.14.0"
jsonschema = { version = "0.33.0", default-features = false }
jsonwebtoken = { version = "^10.0.0", default-features = false, features = ["use_pem", "rust_crypto"] }
kanal = "0.1.1"
lazy_static = "1.4.0"
lettre = { version = "^0.11.7", default-features = false, features = ["tokio1-rustls-tls", "sendmail-transport", "smtp-transport", "builder"] }
log = { version = "^0.4.21", default-features = false }
mini-moka = "0.10.3"
minijinja = { workspace = true }
oauth2 = { version = "5.0.0-alpha.4", default-features = false, features = ["reqwest", "rustls-tls"] }
object_store = { version = "0.12.0", default-features = false, features = ["aws", "fs"] }
parking_lot = { workspace = true }
pin-project-lite = "0.2.16"
prost = { version = "^0.14.1", default-features = false }
prost-reflect = { version = "^0.16.0", default-features = false, features = ["derive", "text-format"] }
rand = { workspace = true }
reactivate = { version = "0.4.2", features = ["threadsafe"] }
regex = "1.11.0"
reqwest = { workspace = true }
rusqlite = { workspace = true }
rust-embed = { workspace = true }
serde = { version = "^1.0.203", features = ["derive"] }
serde_json = "^1.0.117"
serde_path_to_error = "0.1.16"
serde_urlencoded = "0.7.1"
sha2 = "0.10.8"
sqlformat = "0.4.0"
sqlite3-parser = "0.15.0"
thiserror = "2.0.12"
tokio = { workspace = true }
tokio-rustls = { version = "0.26.1", default-features = false }
tower = "0.5.0"
tower-cookies = "0.11.0"
tower-http = { version = "^0.6.0", default-features = false, features = ["cors", "trace", "fs", "limit"] }
tower-service = { version = "0.3.3", default-features = false }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
trailbase-assets = { workspace = true }
trailbase-build = { workspace = true }
trailbase-extension = { workspace = true }
trailbase-js = { workspace = true, optional = true }
trailbase-qs = { workspace = true }
trailbase-refinery = { workspace = true }
trailbase-schema = { workspace = true }
trailbase-sqlite = { workspace = true }
trailbase-wasm-runtime-host = { workspace = true, optional = true }
trailbase-wasm-common = { workspace = true }
ts-rs = { workspace = true }
url = { version = "^2.4.1", default-features = false }
utoipa = { version = "5.0.0-beta.0", features = ["axum_extras"] }
uuid = { workspace = true }
validator = { version = "0.20.0", default-features = false }
indexmap = "2.11.4"

[build-dependencies]
trailbase-build = { workspace = true }

[dev-dependencies]
anyhow = "^1.0.86"
axum-test = "17.0.1"
criterion = { version = "0.7", features = ["html_reports", "async_tokio"] }
env_logger = { workspace = true }
trailbase-extension = { workspace = true }
quoted_printable = "0.5.1"
schemars = "1.0.0"
temp-dir = "0.1.13"
tower = { version = "0.5.0", features = ["util"] }
rcgen = "0.14.1"
