[package]
name = "trailbase-client"
version = "0.5.0"
edition = "2024"
license = "OSL-3.0"
description = "Client for accessing TrailBase's record APIs"
homepage = "https://trailbase.io"
repository = "https://github.com/trailbaseio/trailbase"
readme = "../../README.md"
exclude = [
  "tests",
]

[dependencies]
eventsource-stream = { version = "0.2.3", features = [] }
futures-lite = "2.6.1"
jsonwebtoken = { version = "9.3.1", default-features = false }
parking_lot = { workspace = true }
reqwest = { version = "0.12.8", features = ["stream"] }
serde = { version = "1.0.217", features = ["derive"] }
serde_json = "1.0.135"
thiserror = "2.0.12"
tracing = "0.1.41"
url = "2.5.4"

[dev-dependencies]
base64 = { version = "0.22.1", default-features = false, features = ["alloc"] }
lazy_static = "1.4.0"
reqwest = { version = "0.12.8", features = ["stream", "multipart", "json"] }
temp-dir = "0.1.13"
tokio = { version = "1.43.0", features = ["macros", "rt-multi-thread"] }
