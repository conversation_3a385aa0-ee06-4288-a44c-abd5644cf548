{"name": "trailbase-js-runtime", "description": "Runtime for JS/TS execution within TrailBase", "version": "0.1.0", "license": "OSL-3.0", "type": "module", "homepage": "https://trailbase.io", "scripts": {"build": "vite build", "check": "tsc --noEmit --skipLibCheck && eslint && vitest run", "format": "prettier -w src", "test": "vitest run"}, "devDependencies": {"@eslint/js": "^9.37.0", "eslint": "^9.37.0", "prettier": "^3.6.2", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vite": "^7.1.9", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}}