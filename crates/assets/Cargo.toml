[package]
name = "trailbase-assets"
version = "0.2.0"
edition = "2024"
license = "OSL-3.0"
description = "Assets for the TrailBase framework"
homepage = "https://trailbase.io"
repository = "https://github.com/trailbaseio/trailbase"
readme = "../README.md"
exclude = [
  "**/node_modules/",
  "**/dist/",
]

[dependencies]
askama = { workspace = true }
axum = { workspace = true }
itertools = "0.14.0"
log = "0.4.27"
rust-embed = { workspace = true }
tower-service = { version = "0.3.3", default-features = false }

[build-dependencies]
trailbase-build = { workspace = true }
