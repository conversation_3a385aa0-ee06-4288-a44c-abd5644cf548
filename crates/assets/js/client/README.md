# TrailBase Client

TrailBase is a [blazingly](https://trailbase.io/reference/benchmarks/) fast,
open-source application server with type-safe APIs, built-in JS/ES6/TS runtime,
realtime, auth, and admin UI built on Rust, SQLite & V8.

For more context, documentation, and an online demo, check out the website:
[trailbase.io](https://trailbase.io).

This is the first-party client for hooking up your web and headless (node.js,
deno, ...) apps with TrailBase.
While we're working on better documentation, an example web app can be found in
the repository under:
[`/examples/blog/web`](https://github.com/trailbaseio/trailbase/tree/main/examples/blog/web).
