{"name": "trailbase", "description": "Official TrailBase Client", "homepage": "https://trailbase.io", "version": "0.8.0", "license": "OSL-3.0", "type": "module", "main": "./src/index.ts", "publishConfig": {"access": "public", "main": "./dist/client/src/index.js", "types": "/dist/client/src/index.d.ts", "exports": {".": {"default": "./dist/client/src/index.js", "types": "./dist/client/src/index.d.ts"}}}, "files": ["dist", "package.json"], "repository": {"type": "git", "url": "https://github.com/trailbaseio/trailbase.git", "directory": "crates/assets/js/client"}, "scripts": {"build": "tsc", "check": "tsc --noEmit --skipLibCheck && eslint", "format": "prettier -w src tests", "prepack": "rm -rf ./dist && pnpm build && test -e ./dist/client/src/index.js", "start": "tsc && node dist/client/src/index.js", "test": "vitest run && vite-node tests/integration_test_runner.ts"}, "devDependencies": {"@eslint/js": "^9.37.0", "eslint": "^9.37.0", "execa": "^9.6.0", "globals": "^16.4.0", "http-status": "^2.1.0", "jsdom": "^27.0.0", "oauth2-mock-server": "^8.1.0", "prettier": "^3.6.2", "tinybench": "^5.0.1", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vite-node": "^3.2.4", "vitest": "^3.2.4"}, "dependencies": {"jwt-decode": "^4.0.0", "uuid": "^13.0.0"}}