// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { ColumnMapping } from "./ColumnMapping";
import type { QualifiedName } from "./QualifiedName";

export type View = { name: QualifiedName, 
/**
 * Columns may be inferred from a view's query.
 *
 * Views can be defined with arbitrary queries referencing arbitrary sources: tables, views,
 * functions, ..., which makes them inherently not type safe and therefore their columns not
 * well defined.
 *
 * QUESTION: We've been wondering if the inference should live more in ViewMetadata, however
 * right now the `View` is heavily used in the UI to e.g. render tables and infer record API
 * suitability. It's ok that this is more than just an AST.
 */
column_mapping: ColumnMapping | null, query: string, temporary: boolean, };
