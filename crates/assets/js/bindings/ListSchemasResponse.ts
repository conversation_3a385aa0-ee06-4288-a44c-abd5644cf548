// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { Table } from "./Table";
import type { TableIndex } from "./TableIndex";
import type { TableTrigger } from "./TableTrigger";
import type { View } from "./View";

export type ListSchemasResponse = { tables: Array<Table>, indexes: Array<TableIndex>, triggers: Array<TableTrigger>, views: Array<View>, };
