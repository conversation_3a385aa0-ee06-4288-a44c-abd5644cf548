// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.

export type InfoResponse = { 
/**
 * Build metadata.
 */
compiler: string | null, 
/**
 * Git metadata
 */
commit_hash: string | null, commit_date: string | null, git_version: [string, number] | null, 
/**
 * Runtime metadata.
 */
threads: number, command_line_arguments: Array<string> | null, };
