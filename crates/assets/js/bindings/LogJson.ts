// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { GeoipCity } from "./GeoipCity";

export type LogJson = { id: bigint, created: number, status: number, method: string, url: string, latency_ms: number, client_ip: string, 
/**
 * Optional two-letter country code.
 */
client_geoip_cc: string | null, client_geoip_city: GeoipCity | null, referer: string, user_agent: string, user_id: string | null, };
