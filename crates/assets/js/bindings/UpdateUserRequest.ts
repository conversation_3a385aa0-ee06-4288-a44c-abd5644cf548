// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.

/**
 * Request changes to user with given `id`.
 *
 * NOTE: We don't allow admin promotions and especially demotions, since they could easily be
 * abused. Instead we relegate such critical actions to the CLI, which limits them to sys
 * admins over mere TrailBase admins.
 */
export type UpdateUserRequest = { id: string, email: string | null, password: string | null, verified: boolean | null, };
