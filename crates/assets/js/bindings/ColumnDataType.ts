// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.

export type ColumnDataType = "Null" | "Any" | "Blob" | "Text" | "Integer" | "Real" | "Numeric" | "JSON" | "JSONB" | "Int" | "TinyInt" | "SmallInt" | "MediumInt" | "BigInt" | "UnignedBigInt" | "Int2" | "Int4" | "Int8" | "Character" | "Varchar" | "VaryingCharacter" | "NChar" | "NativeCharacter" | "NVarChar" | "Clob" | "Double" | "DoublePrecision" | "Float" | "Boolean" | "Decimal" | "Date" | "DateTime";
