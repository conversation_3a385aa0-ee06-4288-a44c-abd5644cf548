// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.
import type { ConflictResolution } from "./ConflictResolution";

export type UniqueConstraint = { name: string | null, 
/**
 * Identifiers of the columns that are unique.
 *
 * TODO: Should be indexed/ordered column, e.g. ASC/DESC:
 *   https://www.sqlite.org/syntax/indexed-column.html
 */
columns: Array<string>, conflict_clause: ConflictResolution | null, };
