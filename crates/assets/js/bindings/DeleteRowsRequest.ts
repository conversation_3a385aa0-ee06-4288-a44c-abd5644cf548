// This file was generated by [ts-rs](https://github.com/Aleph-Alpha/ts-rs). Do not edit this file manually.

export type DeleteRowsRequest = { 
/**
 * Name of the primary key column we use to identify which rows to delete.
 */
primary_key_column: string, 
/**
 * A list of primary keys (of any type since we're in row instead of RecordApi land)
 * of rows that shall be deleted.
 */
values: Object[], };
