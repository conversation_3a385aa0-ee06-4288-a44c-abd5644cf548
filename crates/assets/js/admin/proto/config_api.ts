// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.7
//   protoc               v3.21.12
// source: config_api.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Config } from "./config";

export const protobufPackage = "config";

export interface GetConfigResponse {
  config?: Config | undefined;
  hash?: string | undefined;
}

export interface UpdateConfigRequest {
  config?: Config | undefined;
  hash?: string | undefined;
}

function createBaseGetConfigResponse(): GetConfigResponse {
  return {};
}

export const GetConfigResponse: MessageFns<GetConfigResponse> = {
  encode(message: GetConfigResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.config !== undefined) {
      Config.encode(message.config, writer.uint32(10).fork()).join();
    }
    if (message.hash !== undefined && message.hash !== "") {
      writer.uint32(18).string(message.hash);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetConfigResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetConfigResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.config = Config.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.hash = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetConfigResponse {
    return {
      config: isSet(object.config) ? Config.fromJSON(object.config) : undefined,
      hash: isSet(object.hash) ? globalThis.String(object.hash) : undefined,
    };
  },

  toJSON(message: GetConfigResponse): unknown {
    const obj: any = {};
    if (message.config !== undefined) {
      obj.config = Config.toJSON(message.config);
    }
    if (message.hash !== undefined && message.hash !== "") {
      obj.hash = message.hash;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetConfigResponse>, I>>(base?: I): GetConfigResponse {
    return GetConfigResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetConfigResponse>, I>>(object: I): GetConfigResponse {
    const message = createBaseGetConfigResponse();
    message.config = (object.config !== undefined && object.config !== null)
      ? Config.fromPartial(object.config)
      : undefined;
    message.hash = object.hash ?? "";
    return message;
  },
};

function createBaseUpdateConfigRequest(): UpdateConfigRequest {
  return {};
}

export const UpdateConfigRequest: MessageFns<UpdateConfigRequest> = {
  encode(message: UpdateConfigRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.config !== undefined) {
      Config.encode(message.config, writer.uint32(10).fork()).join();
    }
    if (message.hash !== undefined && message.hash !== "") {
      writer.uint32(18).string(message.hash);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateConfigRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateConfigRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.config = Config.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.hash = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateConfigRequest {
    return {
      config: isSet(object.config) ? Config.fromJSON(object.config) : undefined,
      hash: isSet(object.hash) ? globalThis.String(object.hash) : undefined,
    };
  },

  toJSON(message: UpdateConfigRequest): unknown {
    const obj: any = {};
    if (message.config !== undefined) {
      obj.config = Config.toJSON(message.config);
    }
    if (message.hash !== undefined && message.hash !== "") {
      obj.hash = message.hash;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateConfigRequest>, I>>(base?: I): UpdateConfigRequest {
    return UpdateConfigRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateConfigRequest>, I>>(object: I): UpdateConfigRequest {
    const message = createBaseUpdateConfigRequest();
    message.config = (object.config !== undefined && object.config !== null)
      ? Config.fromPartial(object.config)
      : undefined;
    message.hash = object.hash ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
