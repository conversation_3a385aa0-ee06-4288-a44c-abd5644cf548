// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.7
//   protoc               v3.21.12
// source: config.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "config";

export enum SmtpEncryption {
  SMTP_ENCRYPTION_UNDEFINED = 0,
  SMTP_ENCRYPTION_NONE = 1,
  SMTP_ENCRYPTION_STARTTLS = 2,
  SMTP_ENCRYPTION_TLS = 3,
  UNRECOGNIZED = -1,
}

export function smtpEncryptionFromJSON(object: any): SmtpEncryption {
  switch (object) {
    case 0:
    case "SMTP_ENCRYPTION_UNDEFINED":
      return SmtpEncryption.SMTP_ENCRYPTION_UNDEFINED;
    case 1:
    case "SMTP_ENCRYPTION_NONE":
      return SmtpEncryption.SMTP_ENCRYPTION_NONE;
    case 2:
    case "SMTP_ENCRYPTION_STARTTLS":
      return SmtpEncryption.SMTP_ENCRYPTION_STARTTLS;
    case 3:
    case "SMTP_ENCRYPTION_TLS":
      return SmtpEncryption.SMTP_ENCRYPTION_TLS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SmtpEncryption.UNRECOGNIZED;
  }
}

export function smtpEncryptionToJSON(object: SmtpEncryption): string {
  switch (object) {
    case SmtpEncryption.SMTP_ENCRYPTION_UNDEFINED:
      return "SMTP_ENCRYPTION_UNDEFINED";
    case SmtpEncryption.SMTP_ENCRYPTION_NONE:
      return "SMTP_ENCRYPTION_NONE";
    case SmtpEncryption.SMTP_ENCRYPTION_STARTTLS:
      return "SMTP_ENCRYPTION_STARTTLS";
    case SmtpEncryption.SMTP_ENCRYPTION_TLS:
      return "SMTP_ENCRYPTION_TLS";
    case SmtpEncryption.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum OAuthProviderId {
  OAUTH_PROVIDER_ID_UNDEFINED = 0,
  TEST = 1,
  OIDC0 = 2,
  DISCORD = 10,
  GITLAB = 11,
  GOOGLE = 12,
  FACEBOOK = 13,
  MICROSOFT = 14,
  UNRECOGNIZED = -1,
}

export function oAuthProviderIdFromJSON(object: any): OAuthProviderId {
  switch (object) {
    case 0:
    case "OAUTH_PROVIDER_ID_UNDEFINED":
      return OAuthProviderId.OAUTH_PROVIDER_ID_UNDEFINED;
    case 1:
    case "TEST":
      return OAuthProviderId.TEST;
    case 2:
    case "OIDC0":
      return OAuthProviderId.OIDC0;
    case 10:
    case "DISCORD":
      return OAuthProviderId.DISCORD;
    case 11:
    case "GITLAB":
      return OAuthProviderId.GITLAB;
    case 12:
    case "GOOGLE":
      return OAuthProviderId.GOOGLE;
    case 13:
    case "FACEBOOK":
      return OAuthProviderId.FACEBOOK;
    case 14:
    case "MICROSOFT":
      return OAuthProviderId.MICROSOFT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return OAuthProviderId.UNRECOGNIZED;
  }
}

export function oAuthProviderIdToJSON(object: OAuthProviderId): string {
  switch (object) {
    case OAuthProviderId.OAUTH_PROVIDER_ID_UNDEFINED:
      return "OAUTH_PROVIDER_ID_UNDEFINED";
    case OAuthProviderId.TEST:
      return "TEST";
    case OAuthProviderId.OIDC0:
      return "OIDC0";
    case OAuthProviderId.DISCORD:
      return "DISCORD";
    case OAuthProviderId.GITLAB:
      return "GITLAB";
    case OAuthProviderId.GOOGLE:
      return "GOOGLE";
    case OAuthProviderId.FACEBOOK:
      return "FACEBOOK";
    case OAuthProviderId.MICROSOFT:
      return "MICROSOFT";
    case OAuthProviderId.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SystemJobId {
  SYSTEM_JOB_ID_UNDEFINED = 0,
  BACKUP = 1,
  HEARTBEAT = 2,
  LOG_CLEANER = 3,
  AUTH_CLEANER = 4,
  QUERY_OPTIMIZER = 5,
  FILE_DELETIONS = 6,
  UNRECOGNIZED = -1,
}

export function systemJobIdFromJSON(object: any): SystemJobId {
  switch (object) {
    case 0:
    case "SYSTEM_JOB_ID_UNDEFINED":
      return SystemJobId.SYSTEM_JOB_ID_UNDEFINED;
    case 1:
    case "BACKUP":
      return SystemJobId.BACKUP;
    case 2:
    case "HEARTBEAT":
      return SystemJobId.HEARTBEAT;
    case 3:
    case "LOG_CLEANER":
      return SystemJobId.LOG_CLEANER;
    case 4:
    case "AUTH_CLEANER":
      return SystemJobId.AUTH_CLEANER;
    case 5:
    case "QUERY_OPTIMIZER":
      return SystemJobId.QUERY_OPTIMIZER;
    case 6:
    case "FILE_DELETIONS":
      return SystemJobId.FILE_DELETIONS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SystemJobId.UNRECOGNIZED;
  }
}

export function systemJobIdToJSON(object: SystemJobId): string {
  switch (object) {
    case SystemJobId.SYSTEM_JOB_ID_UNDEFINED:
      return "SYSTEM_JOB_ID_UNDEFINED";
    case SystemJobId.BACKUP:
      return "BACKUP";
    case SystemJobId.HEARTBEAT:
      return "HEARTBEAT";
    case SystemJobId.LOG_CLEANER:
      return "LOG_CLEANER";
    case SystemJobId.AUTH_CLEANER:
      return "AUTH_CLEANER";
    case SystemJobId.QUERY_OPTIMIZER:
      return "QUERY_OPTIMIZER";
    case SystemJobId.FILE_DELETIONS:
      return "FILE_DELETIONS";
    case SystemJobId.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/**
 * / Sqlite specific (as opposed to standard SQL) constrained-violation
 * / resolution strategy upon insert.
 */
export enum ConflictResolutionStrategy {
  CONFLICT_RESOLUTION_STRATEGY_UNDEFINED = 0,
  /** ABORT - / SQL default: Keep transaction open and abort the current statement. */
  ABORT = 1,
  /** ROLLBACK - / Abort entire transaction. */
  ROLLBACK = 2,
  /**
   * FAIL - / Similar to Abort but doesn't roll back the current statement, i.e. if the
   * / current statement affects multiple rows, changes by that statement prior
   * / to the failure are not rolled back.
   */
  FAIL = 3,
  /** IGNORE - / Skip the statement and continue. */
  IGNORE = 4,
  /**
   * REPLACE - / Replaces the conflicting row in case of a collision (e.g. unique
   * / constraint).
   */
  REPLACE = 5,
  UNRECOGNIZED = -1,
}

export function conflictResolutionStrategyFromJSON(object: any): ConflictResolutionStrategy {
  switch (object) {
    case 0:
    case "CONFLICT_RESOLUTION_STRATEGY_UNDEFINED":
      return ConflictResolutionStrategy.CONFLICT_RESOLUTION_STRATEGY_UNDEFINED;
    case 1:
    case "ABORT":
      return ConflictResolutionStrategy.ABORT;
    case 2:
    case "ROLLBACK":
      return ConflictResolutionStrategy.ROLLBACK;
    case 3:
    case "FAIL":
      return ConflictResolutionStrategy.FAIL;
    case 4:
    case "IGNORE":
      return ConflictResolutionStrategy.IGNORE;
    case 5:
    case "REPLACE":
      return ConflictResolutionStrategy.REPLACE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ConflictResolutionStrategy.UNRECOGNIZED;
  }
}

export function conflictResolutionStrategyToJSON(object: ConflictResolutionStrategy): string {
  switch (object) {
    case ConflictResolutionStrategy.CONFLICT_RESOLUTION_STRATEGY_UNDEFINED:
      return "CONFLICT_RESOLUTION_STRATEGY_UNDEFINED";
    case ConflictResolutionStrategy.ABORT:
      return "ABORT";
    case ConflictResolutionStrategy.ROLLBACK:
      return "ROLLBACK";
    case ConflictResolutionStrategy.FAIL:
      return "FAIL";
    case ConflictResolutionStrategy.IGNORE:
      return "IGNORE";
    case ConflictResolutionStrategy.REPLACE:
      return "REPLACE";
    case ConflictResolutionStrategy.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum PermissionFlag {
  PERMISSION_FLAG_UNDEFINED = 0,
  /** CREATE - Database record insert. */
  CREATE = 1,
  /** READ - Database record read/list, i.e. select. */
  READ = 2,
  /** UPDATE - Database record update. */
  UPDATE = 4,
  /** DELETE - Database record delete. */
  DELETE = 8,
  /** SCHEMA - / Lookup JSON schema for the given record api . */
  SCHEMA = 16,
  UNRECOGNIZED = -1,
}

export function permissionFlagFromJSON(object: any): PermissionFlag {
  switch (object) {
    case 0:
    case "PERMISSION_FLAG_UNDEFINED":
      return PermissionFlag.PERMISSION_FLAG_UNDEFINED;
    case 1:
    case "CREATE":
      return PermissionFlag.CREATE;
    case 2:
    case "READ":
      return PermissionFlag.READ;
    case 4:
    case "UPDATE":
      return PermissionFlag.UPDATE;
    case 8:
    case "DELETE":
      return PermissionFlag.DELETE;
    case 16:
    case "SCHEMA":
      return PermissionFlag.SCHEMA;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PermissionFlag.UNRECOGNIZED;
  }
}

export function permissionFlagToJSON(object: PermissionFlag): string {
  switch (object) {
    case PermissionFlag.PERMISSION_FLAG_UNDEFINED:
      return "PERMISSION_FLAG_UNDEFINED";
    case PermissionFlag.CREATE:
      return "CREATE";
    case PermissionFlag.READ:
      return "READ";
    case PermissionFlag.UPDATE:
      return "UPDATE";
    case PermissionFlag.DELETE:
      return "DELETE";
    case PermissionFlag.SCHEMA:
      return "SCHEMA";
    case PermissionFlag.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface EmailTemplate {
  subject?: string | undefined;
  body?: string | undefined;
}

export interface EmailConfig {
  smtpHost?: string | undefined;
  smtpPort?: number | undefined;
  smtpUsername?: string | undefined;
  smtpPassword?:
    | string
    | undefined;
  /** Which encryption method to use. STARTTLS by default. */
  smtpEncryption?: SmtpEncryption | undefined;
  senderName?: string | undefined;
  senderAddress?: string | undefined;
  userVerificationTemplate?: EmailTemplate | undefined;
  passwordResetTemplate?: EmailTemplate | undefined;
  changeEmailTemplate?: EmailTemplate | undefined;
}

export interface OAuthProviderConfig {
  clientId?: string | undefined;
  clientSecret?: string | undefined;
  providerId?:
    | OAuthProviderId
    | undefined;
  /**
   * Settings for generic OpenID Connect provider. Name is implicitly provided
   * via the `AuthConfig.oauth_provders` map key.
   */
  displayName?: string | undefined;
  authUrl?: string | undefined;
  tokenUrl?: string | undefined;
  userApiUrl?: string | undefined;
}

export interface AuthConfig {
  /** / Time-to-live in seconds for auth tokens. Default: 1h. */
  authTokenTtlSec?:
    | number
    | undefined;
  /** / Time-to-live in seconds for refresh tokens. Default: 30 days. */
  refreshTokenTtlSec?:
    | number
    | undefined;
  /** / Disables password-based sign-up. Does not affect already registered users. */
  disablePasswordAuth?:
    | boolean
    | undefined;
  /** / Minimal password length. Defaults to 8. */
  passwordMinimalLength?:
    | number
    | undefined;
  /** / Password must contain lower and upper-case letters. */
  passwordMustContainUpperAndLowerCase?:
    | boolean
    | undefined;
  /** / Password must contain digits in addition to alphabetic characters.. */
  passwordMustContainDigits?:
    | boolean
    | undefined;
  /** / Password must contain special, non-alphanumeric, characters. */
  passwordMustContainSpecialCharacters?:
    | boolean
    | undefined;
  /** / Map of configured OAuth providers. */
  oauthProviders: { [key: string]: OAuthProviderConfig };
  /**
   * / List of custom URI schemes allowed as auth redirects.
   * /
   * / This is useful for mobile apps, desktop or SPAs where an app registers a
   * / custom scheme for calls it wants to handle.
   */
  customUriSchemes: string[];
}

export interface AuthConfig_OauthProvidersEntry {
  key: string;
  value: OAuthProviderConfig | undefined;
}

export interface S3StorageConfig {
  endpoint?: string | undefined;
  region?: string | undefined;
  bucketName?:
    | string
    | undefined;
  /** / S3 access key, a.k.a. username. */
  accessKey?:
    | string
    | undefined;
  /** / S3 secret access key, a.k.a. password. */
  secretAccessKey?: string | undefined;
}

export interface ServerConfig {
  /**
   * / Application name presented to users, e.g. when sending emails. Default:
   * / "TrailBase".
   */
  applicationName?:
    | string
    | undefined;
  /**
   * / Your final, deployed URL. This url is used to build canonical urls
   * / for emails, OAuth redirects, ... . Default: "http://localhost:4000".
   */
  siteUrl?:
    | string
    | undefined;
  /**
   * /  Max age of logs that will be retained during period logs cleanup. Note
   * /  that this implies that some older logs may persist until the cleanup job
   * /  reruns. Default: 7 days.
   */
  logsRetentionSec?:
    | number
    | undefined;
  /** / If present will use S3 setup over local file-system based storage. */
  s3StorageConfig?:
    | S3StorageConfig
    | undefined;
  /** / If enabled, batches of transactions can be submitted for attomic execution */
  enableRecordTransactions?: boolean | undefined;
}

export interface SystemJob {
  /** / Identifies the system job by its id. */
  id?:
    | SystemJobId
    | undefined;
  /**
   * / Cron spec: shorthand or 7-components: (sec, min, hour, day of month, /
   * / month, day of week, year).
   */
  schedule?:
    | string
    | undefined;
  /** / Disable the system job. */
  disabled?: boolean | undefined;
}

export interface JobsConfig {
  /**
   * / System jobs overrides.
   * /
   * / NOTE: This is technically a map from id to config, however enums are not
   * / allowed as map keys.
   */
  systemJobs: SystemJob[];
}

export interface RecordApiConfig {
  /** / API name, i.e. unique name used to access data via HTTP. */
  name?:
    | string
    | undefined;
  /** / Referenced table to be exposed. */
  tableName?:
    | string
    | undefined;
  /** / Strategy to be used on insert if a table constraint is violated. */
  conflictResolution?:
    | ConflictResolutionStrategy
    | undefined;
  /**
   * / Fill columns referencing _user(id) automatically from current user's
   * / authentication context if present. Can be useful for static clients, such
   * / as HTML forms, otherwise it's recommended to have the client provide user
   * / ids explicitly and to keep this feature off.
   */
  autofillMissingUserIdColumns?:
    | boolean
    | undefined;
  /**
   * / Allow subscribing to data changes in realtime using SSE streaming.
   * /
   * / NOTE: If you're using a reverse proxy, this will likely require
   * / configuration changes to allow for server-side streaming HTTP, e.g.
   * / tell the proxy to keep listening and not cache.
   */
  enableSubscriptions?:
    | boolean
    | undefined;
  /** / Access control lists. */
  aclWorld: PermissionFlag[];
  aclAuthenticated: PermissionFlag[];
  /**
   * / Columns excluded from this API.
   * /
   * / Excluded columns are completely inaccessible via this API. This is
   * / different from columns with names prefixed by "_", which are considered
   * / hidden. This means they can still be inserted, updated and access checked
   * / unlike excluded columns.
   */
  excludedColumns: string[];
  /**
   * / Access rules to be evaluated on request. Expected to be valid SQL
   * / expression, where `SELECT <expr>` returns a unary boolean.
   * /
   * / The evaluator injects _USER_, _ROW_ and _REQ_ tables that can be
   * / used for validating access, e.g.:
   * /
   * /   _USER_.id = _REQ_.owner
   * /
   * / ensures that the value provided for the `owner` column during an insert
   * / matches the current authenticated user's id. One can also construct
   * / arbitrary validations including sub-queries, e.g.:
   * /
   * /   _USER_.id = _REQ_.owner AND EXISTS(SELECT FROM allowed WHERE
   * /   allowed.user = _USER_.id)
   */
  createAccessRule?: string | undefined;
  readAccessRule?: string | undefined;
  updateAccessRule?: string | undefined;
  deleteAccessRule?: string | undefined;
  schemaAccessRule?:
    | string
    | undefined;
  /**
   * / A list of foreign key columns that *can* be expanded on read/list, i.e.
   * / the foreign record will be inlined into the response. By default nothing
   * / is expanded.
   * /
   * / Only columns and foreign tables with names not starting with "_", i.e. are
   * / allowed to be expanded.
   */
  expand: string[];
  /** / Hard limit for listing records (default: 1024). */
  listingHardLimit?: number | undefined;
}

export interface JsonSchemaConfig {
  name?: string | undefined;
  schema?: string | undefined;
}

export interface Config {
  /**
   * NOTE: These top-level fields currently have to be `required` due to the
   * overly simple approach on how we do config merging (from env vars and
   * vault).
   */
  email: EmailConfig | undefined;
  server: ServerConfig | undefined;
  auth: AuthConfig | undefined;
  jobs: JobsConfig | undefined;
  recordApis: RecordApiConfig[];
  schemas: JsonSchemaConfig[];
}

function createBaseEmailTemplate(): EmailTemplate {
  return {};
}

export const EmailTemplate: MessageFns<EmailTemplate> = {
  encode(message: EmailTemplate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.subject !== undefined && message.subject !== "") {
      writer.uint32(10).string(message.subject);
    }
    if (message.body !== undefined && message.body !== "") {
      writer.uint32(18).string(message.body);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailTemplate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailTemplate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.subject = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.body = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmailTemplate {
    return {
      subject: isSet(object.subject) ? globalThis.String(object.subject) : undefined,
      body: isSet(object.body) ? globalThis.String(object.body) : undefined,
    };
  },

  toJSON(message: EmailTemplate): unknown {
    const obj: any = {};
    if (message.subject !== undefined && message.subject !== "") {
      obj.subject = message.subject;
    }
    if (message.body !== undefined && message.body !== "") {
      obj.body = message.body;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmailTemplate>, I>>(base?: I): EmailTemplate {
    return EmailTemplate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmailTemplate>, I>>(object: I): EmailTemplate {
    const message = createBaseEmailTemplate();
    message.subject = object.subject ?? "";
    message.body = object.body ?? "";
    return message;
  },
};

function createBaseEmailConfig(): EmailConfig {
  return {};
}

export const EmailConfig: MessageFns<EmailConfig> = {
  encode(message: EmailConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.smtpHost !== undefined && message.smtpHost !== "") {
      writer.uint32(10).string(message.smtpHost);
    }
    if (message.smtpPort !== undefined && message.smtpPort !== 0) {
      writer.uint32(16).uint32(message.smtpPort);
    }
    if (message.smtpUsername !== undefined && message.smtpUsername !== "") {
      writer.uint32(26).string(message.smtpUsername);
    }
    if (message.smtpPassword !== undefined && message.smtpPassword !== "") {
      writer.uint32(34).string(message.smtpPassword);
    }
    if (message.smtpEncryption !== undefined && message.smtpEncryption !== 0) {
      writer.uint32(40).int32(message.smtpEncryption);
    }
    if (message.senderName !== undefined && message.senderName !== "") {
      writer.uint32(90).string(message.senderName);
    }
    if (message.senderAddress !== undefined && message.senderAddress !== "") {
      writer.uint32(98).string(message.senderAddress);
    }
    if (message.userVerificationTemplate !== undefined) {
      EmailTemplate.encode(message.userVerificationTemplate, writer.uint32(170).fork()).join();
    }
    if (message.passwordResetTemplate !== undefined) {
      EmailTemplate.encode(message.passwordResetTemplate, writer.uint32(178).fork()).join();
    }
    if (message.changeEmailTemplate !== undefined) {
      EmailTemplate.encode(message.changeEmailTemplate, writer.uint32(186).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.smtpHost = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.smtpPort = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.smtpUsername = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.smtpPassword = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.smtpEncryption = reader.int32() as any;
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.senderName = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.senderAddress = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.userVerificationTemplate = EmailTemplate.decode(reader, reader.uint32());
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.passwordResetTemplate = EmailTemplate.decode(reader, reader.uint32());
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.changeEmailTemplate = EmailTemplate.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmailConfig {
    return {
      smtpHost: isSet(object.smtpHost) ? globalThis.String(object.smtpHost) : undefined,
      smtpPort: isSet(object.smtpPort) ? globalThis.Number(object.smtpPort) : undefined,
      smtpUsername: isSet(object.smtpUsername) ? globalThis.String(object.smtpUsername) : undefined,
      smtpPassword: isSet(object.smtpPassword) ? globalThis.String(object.smtpPassword) : undefined,
      smtpEncryption: isSet(object.smtpEncryption) ? smtpEncryptionFromJSON(object.smtpEncryption) : undefined,
      senderName: isSet(object.senderName) ? globalThis.String(object.senderName) : undefined,
      senderAddress: isSet(object.senderAddress) ? globalThis.String(object.senderAddress) : undefined,
      userVerificationTemplate: isSet(object.userVerificationTemplate)
        ? EmailTemplate.fromJSON(object.userVerificationTemplate)
        : undefined,
      passwordResetTemplate: isSet(object.passwordResetTemplate)
        ? EmailTemplate.fromJSON(object.passwordResetTemplate)
        : undefined,
      changeEmailTemplate: isSet(object.changeEmailTemplate)
        ? EmailTemplate.fromJSON(object.changeEmailTemplate)
        : undefined,
    };
  },

  toJSON(message: EmailConfig): unknown {
    const obj: any = {};
    if (message.smtpHost !== undefined && message.smtpHost !== "") {
      obj.smtpHost = message.smtpHost;
    }
    if (message.smtpPort !== undefined && message.smtpPort !== 0) {
      obj.smtpPort = Math.round(message.smtpPort);
    }
    if (message.smtpUsername !== undefined && message.smtpUsername !== "") {
      obj.smtpUsername = message.smtpUsername;
    }
    if (message.smtpPassword !== undefined && message.smtpPassword !== "") {
      obj.smtpPassword = message.smtpPassword;
    }
    if (message.smtpEncryption !== undefined && message.smtpEncryption !== 0) {
      obj.smtpEncryption = smtpEncryptionToJSON(message.smtpEncryption);
    }
    if (message.senderName !== undefined && message.senderName !== "") {
      obj.senderName = message.senderName;
    }
    if (message.senderAddress !== undefined && message.senderAddress !== "") {
      obj.senderAddress = message.senderAddress;
    }
    if (message.userVerificationTemplate !== undefined) {
      obj.userVerificationTemplate = EmailTemplate.toJSON(message.userVerificationTemplate);
    }
    if (message.passwordResetTemplate !== undefined) {
      obj.passwordResetTemplate = EmailTemplate.toJSON(message.passwordResetTemplate);
    }
    if (message.changeEmailTemplate !== undefined) {
      obj.changeEmailTemplate = EmailTemplate.toJSON(message.changeEmailTemplate);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmailConfig>, I>>(base?: I): EmailConfig {
    return EmailConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmailConfig>, I>>(object: I): EmailConfig {
    const message = createBaseEmailConfig();
    message.smtpHost = object.smtpHost ?? "";
    message.smtpPort = object.smtpPort ?? 0;
    message.smtpUsername = object.smtpUsername ?? "";
    message.smtpPassword = object.smtpPassword ?? "";
    message.smtpEncryption = object.smtpEncryption ?? 0;
    message.senderName = object.senderName ?? "";
    message.senderAddress = object.senderAddress ?? "";
    message.userVerificationTemplate =
      (object.userVerificationTemplate !== undefined && object.userVerificationTemplate !== null)
        ? EmailTemplate.fromPartial(object.userVerificationTemplate)
        : undefined;
    message.passwordResetTemplate =
      (object.passwordResetTemplate !== undefined && object.passwordResetTemplate !== null)
        ? EmailTemplate.fromPartial(object.passwordResetTemplate)
        : undefined;
    message.changeEmailTemplate = (object.changeEmailTemplate !== undefined && object.changeEmailTemplate !== null)
      ? EmailTemplate.fromPartial(object.changeEmailTemplate)
      : undefined;
    return message;
  },
};

function createBaseOAuthProviderConfig(): OAuthProviderConfig {
  return {};
}

export const OAuthProviderConfig: MessageFns<OAuthProviderConfig> = {
  encode(message: OAuthProviderConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.clientId !== undefined && message.clientId !== "") {
      writer.uint32(10).string(message.clientId);
    }
    if (message.clientSecret !== undefined && message.clientSecret !== "") {
      writer.uint32(18).string(message.clientSecret);
    }
    if (message.providerId !== undefined && message.providerId !== 0) {
      writer.uint32(24).int32(message.providerId);
    }
    if (message.displayName !== undefined && message.displayName !== "") {
      writer.uint32(90).string(message.displayName);
    }
    if (message.authUrl !== undefined && message.authUrl !== "") {
      writer.uint32(98).string(message.authUrl);
    }
    if (message.tokenUrl !== undefined && message.tokenUrl !== "") {
      writer.uint32(106).string(message.tokenUrl);
    }
    if (message.userApiUrl !== undefined && message.userApiUrl !== "") {
      writer.uint32(114).string(message.userApiUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OAuthProviderConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOAuthProviderConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.clientId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.clientSecret = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.providerId = reader.int32() as any;
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.displayName = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.authUrl = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.tokenUrl = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.userApiUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OAuthProviderConfig {
    return {
      clientId: isSet(object.clientId) ? globalThis.String(object.clientId) : undefined,
      clientSecret: isSet(object.clientSecret) ? globalThis.String(object.clientSecret) : undefined,
      providerId: isSet(object.providerId) ? oAuthProviderIdFromJSON(object.providerId) : undefined,
      displayName: isSet(object.displayName) ? globalThis.String(object.displayName) : undefined,
      authUrl: isSet(object.authUrl) ? globalThis.String(object.authUrl) : undefined,
      tokenUrl: isSet(object.tokenUrl) ? globalThis.String(object.tokenUrl) : undefined,
      userApiUrl: isSet(object.userApiUrl) ? globalThis.String(object.userApiUrl) : undefined,
    };
  },

  toJSON(message: OAuthProviderConfig): unknown {
    const obj: any = {};
    if (message.clientId !== undefined && message.clientId !== "") {
      obj.clientId = message.clientId;
    }
    if (message.clientSecret !== undefined && message.clientSecret !== "") {
      obj.clientSecret = message.clientSecret;
    }
    if (message.providerId !== undefined && message.providerId !== 0) {
      obj.providerId = oAuthProviderIdToJSON(message.providerId);
    }
    if (message.displayName !== undefined && message.displayName !== "") {
      obj.displayName = message.displayName;
    }
    if (message.authUrl !== undefined && message.authUrl !== "") {
      obj.authUrl = message.authUrl;
    }
    if (message.tokenUrl !== undefined && message.tokenUrl !== "") {
      obj.tokenUrl = message.tokenUrl;
    }
    if (message.userApiUrl !== undefined && message.userApiUrl !== "") {
      obj.userApiUrl = message.userApiUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OAuthProviderConfig>, I>>(base?: I): OAuthProviderConfig {
    return OAuthProviderConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OAuthProviderConfig>, I>>(object: I): OAuthProviderConfig {
    const message = createBaseOAuthProviderConfig();
    message.clientId = object.clientId ?? "";
    message.clientSecret = object.clientSecret ?? "";
    message.providerId = object.providerId ?? 0;
    message.displayName = object.displayName ?? "";
    message.authUrl = object.authUrl ?? "";
    message.tokenUrl = object.tokenUrl ?? "";
    message.userApiUrl = object.userApiUrl ?? "";
    return message;
  },
};

function createBaseAuthConfig(): AuthConfig {
  return { oauthProviders: {}, customUriSchemes: [] };
}

export const AuthConfig: MessageFns<AuthConfig> = {
  encode(message: AuthConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.authTokenTtlSec !== undefined && message.authTokenTtlSec !== 0) {
      writer.uint32(8).int64(message.authTokenTtlSec);
    }
    if (message.refreshTokenTtlSec !== undefined && message.refreshTokenTtlSec !== 0) {
      writer.uint32(16).int64(message.refreshTokenTtlSec);
    }
    if (message.disablePasswordAuth !== undefined && message.disablePasswordAuth !== false) {
      writer.uint32(24).bool(message.disablePasswordAuth);
    }
    if (message.passwordMinimalLength !== undefined && message.passwordMinimalLength !== 0) {
      writer.uint32(32).uint32(message.passwordMinimalLength);
    }
    if (
      message.passwordMustContainUpperAndLowerCase !== undefined &&
      message.passwordMustContainUpperAndLowerCase !== false
    ) {
      writer.uint32(40).bool(message.passwordMustContainUpperAndLowerCase);
    }
    if (message.passwordMustContainDigits !== undefined && message.passwordMustContainDigits !== false) {
      writer.uint32(48).bool(message.passwordMustContainDigits);
    }
    if (
      message.passwordMustContainSpecialCharacters !== undefined &&
      message.passwordMustContainSpecialCharacters !== false
    ) {
      writer.uint32(56).bool(message.passwordMustContainSpecialCharacters);
    }
    Object.entries(message.oauthProviders).forEach(([key, value]) => {
      AuthConfig_OauthProvidersEntry.encode({ key: key as any, value }, writer.uint32(90).fork()).join();
    });
    for (const v of message.customUriSchemes) {
      writer.uint32(170).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuthConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuthConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.authTokenTtlSec = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.refreshTokenTtlSec = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.disablePasswordAuth = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.passwordMinimalLength = reader.uint32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.passwordMustContainUpperAndLowerCase = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.passwordMustContainDigits = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.passwordMustContainSpecialCharacters = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          const entry11 = AuthConfig_OauthProvidersEntry.decode(reader, reader.uint32());
          if (entry11.value !== undefined) {
            message.oauthProviders[entry11.key] = entry11.value;
          }
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.customUriSchemes.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AuthConfig {
    return {
      authTokenTtlSec: isSet(object.authTokenTtlSec) ? globalThis.Number(object.authTokenTtlSec) : undefined,
      refreshTokenTtlSec: isSet(object.refreshTokenTtlSec) ? globalThis.Number(object.refreshTokenTtlSec) : undefined,
      disablePasswordAuth: isSet(object.disablePasswordAuth)
        ? globalThis.Boolean(object.disablePasswordAuth)
        : undefined,
      passwordMinimalLength: isSet(object.passwordMinimalLength)
        ? globalThis.Number(object.passwordMinimalLength)
        : undefined,
      passwordMustContainUpperAndLowerCase: isSet(object.passwordMustContainUpperAndLowerCase)
        ? globalThis.Boolean(object.passwordMustContainUpperAndLowerCase)
        : undefined,
      passwordMustContainDigits: isSet(object.passwordMustContainDigits)
        ? globalThis.Boolean(object.passwordMustContainDigits)
        : undefined,
      passwordMustContainSpecialCharacters: isSet(object.passwordMustContainSpecialCharacters)
        ? globalThis.Boolean(object.passwordMustContainSpecialCharacters)
        : undefined,
      oauthProviders: isObject(object.oauthProviders)
        ? Object.entries(object.oauthProviders).reduce<{ [key: string]: OAuthProviderConfig }>((acc, [key, value]) => {
          acc[key] = OAuthProviderConfig.fromJSON(value);
          return acc;
        }, {})
        : {},
      customUriSchemes: globalThis.Array.isArray(object?.customUriSchemes)
        ? object.customUriSchemes.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: AuthConfig): unknown {
    const obj: any = {};
    if (message.authTokenTtlSec !== undefined && message.authTokenTtlSec !== 0) {
      obj.authTokenTtlSec = Math.round(message.authTokenTtlSec);
    }
    if (message.refreshTokenTtlSec !== undefined && message.refreshTokenTtlSec !== 0) {
      obj.refreshTokenTtlSec = Math.round(message.refreshTokenTtlSec);
    }
    if (message.disablePasswordAuth !== undefined && message.disablePasswordAuth !== false) {
      obj.disablePasswordAuth = message.disablePasswordAuth;
    }
    if (message.passwordMinimalLength !== undefined && message.passwordMinimalLength !== 0) {
      obj.passwordMinimalLength = Math.round(message.passwordMinimalLength);
    }
    if (
      message.passwordMustContainUpperAndLowerCase !== undefined &&
      message.passwordMustContainUpperAndLowerCase !== false
    ) {
      obj.passwordMustContainUpperAndLowerCase = message.passwordMustContainUpperAndLowerCase;
    }
    if (message.passwordMustContainDigits !== undefined && message.passwordMustContainDigits !== false) {
      obj.passwordMustContainDigits = message.passwordMustContainDigits;
    }
    if (
      message.passwordMustContainSpecialCharacters !== undefined &&
      message.passwordMustContainSpecialCharacters !== false
    ) {
      obj.passwordMustContainSpecialCharacters = message.passwordMustContainSpecialCharacters;
    }
    if (message.oauthProviders) {
      const entries = Object.entries(message.oauthProviders);
      if (entries.length > 0) {
        obj.oauthProviders = {};
        entries.forEach(([k, v]) => {
          obj.oauthProviders[k] = OAuthProviderConfig.toJSON(v);
        });
      }
    }
    if (message.customUriSchemes?.length) {
      obj.customUriSchemes = message.customUriSchemes;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AuthConfig>, I>>(base?: I): AuthConfig {
    return AuthConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuthConfig>, I>>(object: I): AuthConfig {
    const message = createBaseAuthConfig();
    message.authTokenTtlSec = object.authTokenTtlSec ?? 0;
    message.refreshTokenTtlSec = object.refreshTokenTtlSec ?? 0;
    message.disablePasswordAuth = object.disablePasswordAuth ?? false;
    message.passwordMinimalLength = object.passwordMinimalLength ?? 0;
    message.passwordMustContainUpperAndLowerCase = object.passwordMustContainUpperAndLowerCase ?? false;
    message.passwordMustContainDigits = object.passwordMustContainDigits ?? false;
    message.passwordMustContainSpecialCharacters = object.passwordMustContainSpecialCharacters ?? false;
    message.oauthProviders = Object.entries(object.oauthProviders ?? {}).reduce<{ [key: string]: OAuthProviderConfig }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = OAuthProviderConfig.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.customUriSchemes = object.customUriSchemes?.map((e) => e) || [];
    return message;
  },
};

function createBaseAuthConfig_OauthProvidersEntry(): AuthConfig_OauthProvidersEntry {
  return { key: "", value: undefined };
}

export const AuthConfig_OauthProvidersEntry: MessageFns<AuthConfig_OauthProvidersEntry> = {
  encode(message: AuthConfig_OauthProvidersEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      OAuthProviderConfig.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuthConfig_OauthProvidersEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuthConfig_OauthProvidersEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = OAuthProviderConfig.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AuthConfig_OauthProvidersEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? OAuthProviderConfig.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: AuthConfig_OauthProvidersEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = OAuthProviderConfig.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AuthConfig_OauthProvidersEntry>, I>>(base?: I): AuthConfig_OauthProvidersEntry {
    return AuthConfig_OauthProvidersEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuthConfig_OauthProvidersEntry>, I>>(
    object: I,
  ): AuthConfig_OauthProvidersEntry {
    const message = createBaseAuthConfig_OauthProvidersEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? OAuthProviderConfig.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseS3StorageConfig(): S3StorageConfig {
  return {};
}

export const S3StorageConfig: MessageFns<S3StorageConfig> = {
  encode(message: S3StorageConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.endpoint !== undefined && message.endpoint !== "") {
      writer.uint32(10).string(message.endpoint);
    }
    if (message.region !== undefined && message.region !== "") {
      writer.uint32(18).string(message.region);
    }
    if (message.bucketName !== undefined && message.bucketName !== "") {
      writer.uint32(42).string(message.bucketName);
    }
    if (message.accessKey !== undefined && message.accessKey !== "") {
      writer.uint32(66).string(message.accessKey);
    }
    if (message.secretAccessKey !== undefined && message.secretAccessKey !== "") {
      writer.uint32(74).string(message.secretAccessKey);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): S3StorageConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseS3StorageConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.endpoint = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.region = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bucketName = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.accessKey = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.secretAccessKey = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): S3StorageConfig {
    return {
      endpoint: isSet(object.endpoint) ? globalThis.String(object.endpoint) : undefined,
      region: isSet(object.region) ? globalThis.String(object.region) : undefined,
      bucketName: isSet(object.bucketName) ? globalThis.String(object.bucketName) : undefined,
      accessKey: isSet(object.accessKey) ? globalThis.String(object.accessKey) : undefined,
      secretAccessKey: isSet(object.secretAccessKey) ? globalThis.String(object.secretAccessKey) : undefined,
    };
  },

  toJSON(message: S3StorageConfig): unknown {
    const obj: any = {};
    if (message.endpoint !== undefined && message.endpoint !== "") {
      obj.endpoint = message.endpoint;
    }
    if (message.region !== undefined && message.region !== "") {
      obj.region = message.region;
    }
    if (message.bucketName !== undefined && message.bucketName !== "") {
      obj.bucketName = message.bucketName;
    }
    if (message.accessKey !== undefined && message.accessKey !== "") {
      obj.accessKey = message.accessKey;
    }
    if (message.secretAccessKey !== undefined && message.secretAccessKey !== "") {
      obj.secretAccessKey = message.secretAccessKey;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<S3StorageConfig>, I>>(base?: I): S3StorageConfig {
    return S3StorageConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<S3StorageConfig>, I>>(object: I): S3StorageConfig {
    const message = createBaseS3StorageConfig();
    message.endpoint = object.endpoint ?? "";
    message.region = object.region ?? "";
    message.bucketName = object.bucketName ?? "";
    message.accessKey = object.accessKey ?? "";
    message.secretAccessKey = object.secretAccessKey ?? "";
    return message;
  },
};

function createBaseServerConfig(): ServerConfig {
  return {};
}

export const ServerConfig: MessageFns<ServerConfig> = {
  encode(message: ServerConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.applicationName !== undefined && message.applicationName !== "") {
      writer.uint32(10).string(message.applicationName);
    }
    if (message.siteUrl !== undefined && message.siteUrl !== "") {
      writer.uint32(18).string(message.siteUrl);
    }
    if (message.logsRetentionSec !== undefined && message.logsRetentionSec !== 0) {
      writer.uint32(88).int64(message.logsRetentionSec);
    }
    if (message.s3StorageConfig !== undefined) {
      S3StorageConfig.encode(message.s3StorageConfig, writer.uint32(106).fork()).join();
    }
    if (message.enableRecordTransactions !== undefined && message.enableRecordTransactions !== false) {
      writer.uint32(112).bool(message.enableRecordTransactions);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ServerConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseServerConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.applicationName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.siteUrl = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.logsRetentionSec = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.s3StorageConfig = S3StorageConfig.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.enableRecordTransactions = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ServerConfig {
    return {
      applicationName: isSet(object.applicationName) ? globalThis.String(object.applicationName) : undefined,
      siteUrl: isSet(object.siteUrl) ? globalThis.String(object.siteUrl) : undefined,
      logsRetentionSec: isSet(object.logsRetentionSec) ? globalThis.Number(object.logsRetentionSec) : undefined,
      s3StorageConfig: isSet(object.s3StorageConfig) ? S3StorageConfig.fromJSON(object.s3StorageConfig) : undefined,
      enableRecordTransactions: isSet(object.enableRecordTransactions)
        ? globalThis.Boolean(object.enableRecordTransactions)
        : undefined,
    };
  },

  toJSON(message: ServerConfig): unknown {
    const obj: any = {};
    if (message.applicationName !== undefined && message.applicationName !== "") {
      obj.applicationName = message.applicationName;
    }
    if (message.siteUrl !== undefined && message.siteUrl !== "") {
      obj.siteUrl = message.siteUrl;
    }
    if (message.logsRetentionSec !== undefined && message.logsRetentionSec !== 0) {
      obj.logsRetentionSec = Math.round(message.logsRetentionSec);
    }
    if (message.s3StorageConfig !== undefined) {
      obj.s3StorageConfig = S3StorageConfig.toJSON(message.s3StorageConfig);
    }
    if (message.enableRecordTransactions !== undefined && message.enableRecordTransactions !== false) {
      obj.enableRecordTransactions = message.enableRecordTransactions;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ServerConfig>, I>>(base?: I): ServerConfig {
    return ServerConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServerConfig>, I>>(object: I): ServerConfig {
    const message = createBaseServerConfig();
    message.applicationName = object.applicationName ?? "";
    message.siteUrl = object.siteUrl ?? "";
    message.logsRetentionSec = object.logsRetentionSec ?? 0;
    message.s3StorageConfig = (object.s3StorageConfig !== undefined && object.s3StorageConfig !== null)
      ? S3StorageConfig.fromPartial(object.s3StorageConfig)
      : undefined;
    message.enableRecordTransactions = object.enableRecordTransactions ?? false;
    return message;
  },
};

function createBaseSystemJob(): SystemJob {
  return {};
}

export const SystemJob: MessageFns<SystemJob> = {
  encode(message: SystemJob, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== undefined && message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    if (message.schedule !== undefined && message.schedule !== "") {
      writer.uint32(18).string(message.schedule);
    }
    if (message.disabled !== undefined && message.disabled !== false) {
      writer.uint32(24).bool(message.disabled);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SystemJob {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSystemJob();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.schedule = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.disabled = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SystemJob {
    return {
      id: isSet(object.id) ? systemJobIdFromJSON(object.id) : undefined,
      schedule: isSet(object.schedule) ? globalThis.String(object.schedule) : undefined,
      disabled: isSet(object.disabled) ? globalThis.Boolean(object.disabled) : undefined,
    };
  },

  toJSON(message: SystemJob): unknown {
    const obj: any = {};
    if (message.id !== undefined && message.id !== 0) {
      obj.id = systemJobIdToJSON(message.id);
    }
    if (message.schedule !== undefined && message.schedule !== "") {
      obj.schedule = message.schedule;
    }
    if (message.disabled !== undefined && message.disabled !== false) {
      obj.disabled = message.disabled;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SystemJob>, I>>(base?: I): SystemJob {
    return SystemJob.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SystemJob>, I>>(object: I): SystemJob {
    const message = createBaseSystemJob();
    message.id = object.id ?? 0;
    message.schedule = object.schedule ?? "";
    message.disabled = object.disabled ?? false;
    return message;
  },
};

function createBaseJobsConfig(): JobsConfig {
  return { systemJobs: [] };
}

export const JobsConfig: MessageFns<JobsConfig> = {
  encode(message: JobsConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.systemJobs) {
      SystemJob.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): JobsConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJobsConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.systemJobs.push(SystemJob.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): JobsConfig {
    return {
      systemJobs: globalThis.Array.isArray(object?.systemJobs)
        ? object.systemJobs.map((e: any) => SystemJob.fromJSON(e))
        : [],
    };
  },

  toJSON(message: JobsConfig): unknown {
    const obj: any = {};
    if (message.systemJobs?.length) {
      obj.systemJobs = message.systemJobs.map((e) => SystemJob.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<JobsConfig>, I>>(base?: I): JobsConfig {
    return JobsConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JobsConfig>, I>>(object: I): JobsConfig {
    const message = createBaseJobsConfig();
    message.systemJobs = object.systemJobs?.map((e) => SystemJob.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRecordApiConfig(): RecordApiConfig {
  return { aclWorld: [], aclAuthenticated: [], excludedColumns: [], expand: [] };
}

export const RecordApiConfig: MessageFns<RecordApiConfig> = {
  encode(message: RecordApiConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== undefined && message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.tableName !== undefined && message.tableName !== "") {
      writer.uint32(18).string(message.tableName);
    }
    if (message.conflictResolution !== undefined && message.conflictResolution !== 0) {
      writer.uint32(40).int32(message.conflictResolution);
    }
    if (message.autofillMissingUserIdColumns !== undefined && message.autofillMissingUserIdColumns !== false) {
      writer.uint32(48).bool(message.autofillMissingUserIdColumns);
    }
    if (message.enableSubscriptions !== undefined && message.enableSubscriptions !== false) {
      writer.uint32(72).bool(message.enableSubscriptions);
    }
    for (const v of message.aclWorld) {
      writer.uint32(56).int32(v!);
    }
    for (const v of message.aclAuthenticated) {
      writer.uint32(64).int32(v!);
    }
    for (const v of message.excludedColumns) {
      writer.uint32(82).string(v!);
    }
    if (message.createAccessRule !== undefined && message.createAccessRule !== "") {
      writer.uint32(90).string(message.createAccessRule);
    }
    if (message.readAccessRule !== undefined && message.readAccessRule !== "") {
      writer.uint32(98).string(message.readAccessRule);
    }
    if (message.updateAccessRule !== undefined && message.updateAccessRule !== "") {
      writer.uint32(106).string(message.updateAccessRule);
    }
    if (message.deleteAccessRule !== undefined && message.deleteAccessRule !== "") {
      writer.uint32(114).string(message.deleteAccessRule);
    }
    if (message.schemaAccessRule !== undefined && message.schemaAccessRule !== "") {
      writer.uint32(122).string(message.schemaAccessRule);
    }
    for (const v of message.expand) {
      writer.uint32(170).string(v!);
    }
    if (message.listingHardLimit !== undefined && message.listingHardLimit !== 0) {
      writer.uint32(176).uint64(message.listingHardLimit);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecordApiConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecordApiConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tableName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.conflictResolution = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.autofillMissingUserIdColumns = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.enableSubscriptions = reader.bool();
          continue;
        }
        case 7: {
          if (tag === 56) {
            message.aclWorld.push(reader.int32() as any);

            continue;
          }

          if (tag === 58) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.aclWorld.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 8: {
          if (tag === 64) {
            message.aclAuthenticated.push(reader.int32() as any);

            continue;
          }

          if (tag === 66) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.aclAuthenticated.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.excludedColumns.push(reader.string());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.createAccessRule = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.readAccessRule = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.updateAccessRule = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.deleteAccessRule = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.schemaAccessRule = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.expand.push(reader.string());
          continue;
        }
        case 22: {
          if (tag !== 176) {
            break;
          }

          message.listingHardLimit = longToNumber(reader.uint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecordApiConfig {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      tableName: isSet(object.tableName) ? globalThis.String(object.tableName) : undefined,
      conflictResolution: isSet(object.conflictResolution)
        ? conflictResolutionStrategyFromJSON(object.conflictResolution)
        : undefined,
      autofillMissingUserIdColumns: isSet(object.autofillMissingUserIdColumns)
        ? globalThis.Boolean(object.autofillMissingUserIdColumns)
        : undefined,
      enableSubscriptions: isSet(object.enableSubscriptions)
        ? globalThis.Boolean(object.enableSubscriptions)
        : undefined,
      aclWorld: globalThis.Array.isArray(object?.aclWorld)
        ? object.aclWorld.map((e: any) => permissionFlagFromJSON(e))
        : [],
      aclAuthenticated: globalThis.Array.isArray(object?.aclAuthenticated)
        ? object.aclAuthenticated.map((e: any) => permissionFlagFromJSON(e))
        : [],
      excludedColumns: globalThis.Array.isArray(object?.excludedColumns)
        ? object.excludedColumns.map((e: any) => globalThis.String(e))
        : [],
      createAccessRule: isSet(object.createAccessRule) ? globalThis.String(object.createAccessRule) : undefined,
      readAccessRule: isSet(object.readAccessRule) ? globalThis.String(object.readAccessRule) : undefined,
      updateAccessRule: isSet(object.updateAccessRule) ? globalThis.String(object.updateAccessRule) : undefined,
      deleteAccessRule: isSet(object.deleteAccessRule) ? globalThis.String(object.deleteAccessRule) : undefined,
      schemaAccessRule: isSet(object.schemaAccessRule) ? globalThis.String(object.schemaAccessRule) : undefined,
      expand: globalThis.Array.isArray(object?.expand) ? object.expand.map((e: any) => globalThis.String(e)) : [],
      listingHardLimit: isSet(object.listingHardLimit) ? globalThis.Number(object.listingHardLimit) : undefined,
    };
  },

  toJSON(message: RecordApiConfig): unknown {
    const obj: any = {};
    if (message.name !== undefined && message.name !== "") {
      obj.name = message.name;
    }
    if (message.tableName !== undefined && message.tableName !== "") {
      obj.tableName = message.tableName;
    }
    if (message.conflictResolution !== undefined && message.conflictResolution !== 0) {
      obj.conflictResolution = conflictResolutionStrategyToJSON(message.conflictResolution);
    }
    if (message.autofillMissingUserIdColumns !== undefined && message.autofillMissingUserIdColumns !== false) {
      obj.autofillMissingUserIdColumns = message.autofillMissingUserIdColumns;
    }
    if (message.enableSubscriptions !== undefined && message.enableSubscriptions !== false) {
      obj.enableSubscriptions = message.enableSubscriptions;
    }
    if (message.aclWorld?.length) {
      obj.aclWorld = message.aclWorld.map((e) => permissionFlagToJSON(e));
    }
    if (message.aclAuthenticated?.length) {
      obj.aclAuthenticated = message.aclAuthenticated.map((e) => permissionFlagToJSON(e));
    }
    if (message.excludedColumns?.length) {
      obj.excludedColumns = message.excludedColumns;
    }
    if (message.createAccessRule !== undefined && message.createAccessRule !== "") {
      obj.createAccessRule = message.createAccessRule;
    }
    if (message.readAccessRule !== undefined && message.readAccessRule !== "") {
      obj.readAccessRule = message.readAccessRule;
    }
    if (message.updateAccessRule !== undefined && message.updateAccessRule !== "") {
      obj.updateAccessRule = message.updateAccessRule;
    }
    if (message.deleteAccessRule !== undefined && message.deleteAccessRule !== "") {
      obj.deleteAccessRule = message.deleteAccessRule;
    }
    if (message.schemaAccessRule !== undefined && message.schemaAccessRule !== "") {
      obj.schemaAccessRule = message.schemaAccessRule;
    }
    if (message.expand?.length) {
      obj.expand = message.expand;
    }
    if (message.listingHardLimit !== undefined && message.listingHardLimit !== 0) {
      obj.listingHardLimit = Math.round(message.listingHardLimit);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecordApiConfig>, I>>(base?: I): RecordApiConfig {
    return RecordApiConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecordApiConfig>, I>>(object: I): RecordApiConfig {
    const message = createBaseRecordApiConfig();
    message.name = object.name ?? "";
    message.tableName = object.tableName ?? "";
    message.conflictResolution = object.conflictResolution ?? 0;
    message.autofillMissingUserIdColumns = object.autofillMissingUserIdColumns ?? false;
    message.enableSubscriptions = object.enableSubscriptions ?? false;
    message.aclWorld = object.aclWorld?.map((e) => e) || [];
    message.aclAuthenticated = object.aclAuthenticated?.map((e) => e) || [];
    message.excludedColumns = object.excludedColumns?.map((e) => e) || [];
    message.createAccessRule = object.createAccessRule ?? "";
    message.readAccessRule = object.readAccessRule ?? "";
    message.updateAccessRule = object.updateAccessRule ?? "";
    message.deleteAccessRule = object.deleteAccessRule ?? "";
    message.schemaAccessRule = object.schemaAccessRule ?? "";
    message.expand = object.expand?.map((e) => e) || [];
    message.listingHardLimit = object.listingHardLimit ?? 0;
    return message;
  },
};

function createBaseJsonSchemaConfig(): JsonSchemaConfig {
  return {};
}

export const JsonSchemaConfig: MessageFns<JsonSchemaConfig> = {
  encode(message: JsonSchemaConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== undefined && message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.schema !== undefined && message.schema !== "") {
      writer.uint32(18).string(message.schema);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): JsonSchemaConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJsonSchemaConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.schema = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): JsonSchemaConfig {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      schema: isSet(object.schema) ? globalThis.String(object.schema) : undefined,
    };
  },

  toJSON(message: JsonSchemaConfig): unknown {
    const obj: any = {};
    if (message.name !== undefined && message.name !== "") {
      obj.name = message.name;
    }
    if (message.schema !== undefined && message.schema !== "") {
      obj.schema = message.schema;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<JsonSchemaConfig>, I>>(base?: I): JsonSchemaConfig {
    return JsonSchemaConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JsonSchemaConfig>, I>>(object: I): JsonSchemaConfig {
    const message = createBaseJsonSchemaConfig();
    message.name = object.name ?? "";
    message.schema = object.schema ?? "";
    return message;
  },
};

function createBaseConfig(): Config {
  return { email: undefined, server: undefined, auth: undefined, jobs: undefined, recordApis: [], schemas: [] };
}

export const Config: MessageFns<Config> = {
  encode(message: Config, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.email !== undefined) {
      EmailConfig.encode(message.email, writer.uint32(18).fork()).join();
    }
    if (message.server !== undefined) {
      ServerConfig.encode(message.server, writer.uint32(26).fork()).join();
    }
    if (message.auth !== undefined) {
      AuthConfig.encode(message.auth, writer.uint32(34).fork()).join();
    }
    if (message.jobs !== undefined) {
      JobsConfig.encode(message.jobs, writer.uint32(42).fork()).join();
    }
    for (const v of message.recordApis) {
      RecordApiConfig.encode(v!, writer.uint32(90).fork()).join();
    }
    for (const v of message.schemas) {
      JsonSchemaConfig.encode(v!, writer.uint32(170).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Config {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.email = EmailConfig.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.server = ServerConfig.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.auth = AuthConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.jobs = JobsConfig.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.recordApis.push(RecordApiConfig.decode(reader, reader.uint32()));
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.schemas.push(JsonSchemaConfig.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Config {
    return {
      email: isSet(object.email) ? EmailConfig.fromJSON(object.email) : undefined,
      server: isSet(object.server) ? ServerConfig.fromJSON(object.server) : undefined,
      auth: isSet(object.auth) ? AuthConfig.fromJSON(object.auth) : undefined,
      jobs: isSet(object.jobs) ? JobsConfig.fromJSON(object.jobs) : undefined,
      recordApis: globalThis.Array.isArray(object?.recordApis)
        ? object.recordApis.map((e: any) => RecordApiConfig.fromJSON(e))
        : [],
      schemas: globalThis.Array.isArray(object?.schemas)
        ? object.schemas.map((e: any) => JsonSchemaConfig.fromJSON(e))
        : [],
    };
  },

  toJSON(message: Config): unknown {
    const obj: any = {};
    if (message.email !== undefined) {
      obj.email = EmailConfig.toJSON(message.email);
    }
    if (message.server !== undefined) {
      obj.server = ServerConfig.toJSON(message.server);
    }
    if (message.auth !== undefined) {
      obj.auth = AuthConfig.toJSON(message.auth);
    }
    if (message.jobs !== undefined) {
      obj.jobs = JobsConfig.toJSON(message.jobs);
    }
    if (message.recordApis?.length) {
      obj.recordApis = message.recordApis.map((e) => RecordApiConfig.toJSON(e));
    }
    if (message.schemas?.length) {
      obj.schemas = message.schemas.map((e) => JsonSchemaConfig.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Config>, I>>(base?: I): Config {
    return Config.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Config>, I>>(object: I): Config {
    const message = createBaseConfig();
    message.email = (object.email !== undefined && object.email !== null)
      ? EmailConfig.fromPartial(object.email)
      : undefined;
    message.server = (object.server !== undefined && object.server !== null)
      ? ServerConfig.fromPartial(object.server)
      : undefined;
    message.auth = (object.auth !== undefined && object.auth !== null)
      ? AuthConfig.fromPartial(object.auth)
      : undefined;
    message.jobs = (object.jobs !== undefined && object.jobs !== null)
      ? JobsConfig.fromPartial(object.jobs)
      : undefined;
    message.recordApis = object.recordApis?.map((e) => RecordApiConfig.fromPartial(e)) || [];
    message.schemas = object.schemas?.map((e) => JsonSchemaConfig.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
