{"name": "trailbase-admin-ui", "version": "0.0.1", "description": "TrailBase's Admin Dashboard", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "serve": "vite preview", "format": "prettier -w .", "proto": "protoc --plugin=protoc-gen-ts=${PWD}/node_modules/ts-proto/protoc-gen-ts_proto ../../../core/proto/*.proto -I../../../core//proto -I/usr/include --ts_out=./proto/ --ts_opt=esModuleInterop=true,initializeFieldsAsUndefined=false", "check": "tsc --noEmit --skipLibCheck && eslint && vitest run", "test": "vitest run"}, "dependencies": {"@antv/x6": "^2.18.1", "@bufbuild/protobuf": "^2.9.0", "@codemirror/autocomplete": "^6.19.0", "@codemirror/lang-sql": "^6.10.0", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.38.4", "@corvu/resizable": "^0.2.5", "@kobalte/core": "^0.13.11", "@kobalte/tailwindcss": "^0.9.0", "@kobalte/utils": "^0.9.1", "@nanostores/persistent": "^1.1.0", "@nanostores/solid": "^1.1.1", "@panzoom/panzoom": "^4.6.0", "@solid-primitives/memo": "^1.4.3", "@solidjs/router": "^0.15.3", "@tanstack/solid-form": "^1.23.5", "@tanstack/solid-query": "^5.90.3", "@tanstack/solid-table": "^8.21.3", "@tanstack/table-core": "^8.21.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.2", "geojson": "^0.5.0", "i18n-iso-countries": "^7.14.0", "leaflet": "^1.9.4", "long": "^5.3.2", "nanostores": "^1.0.1", "protobufjs": "^7.5.4", "solid-icons": "^1.1.0", "solid-js": "^1.9.9", "tailwind-merge": "^3.3.1", "trailbase": "file:../client", "uuid": "^13.0.0"}, "devDependencies": {"@eslint/js": "^9.37.0", "@iconify-json/tabler": "^1.2.23", "@solidjs/testing-library": "^0.8.10", "@tailwindcss/typography": "^0.5.19", "@tailwindcss/vite": "^4.1.14", "@testing-library/jest-dom": "^6.9.1", "@testing-library/user-event": "^14.6.1", "@types/geojson": "^7946.0.16", "@types/leaflet": "^1.9.20", "@types/wicg-file-system-access": "^2023.10.7", "autoprefixer": "^10.4.21", "eslint": "^9.37.0", "eslint-plugin-better-tailwindcss": "^3.7.9", "eslint-plugin-solid": "^0.14.5", "globals": "^16.4.0", "jsdom": "^27.0.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.14", "tailwindcss-animate": "^1.0.7", "ts-proto": "^2.7.7", "tw-animate-css": "^1.4.0", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vite": "^7.1.9", "vite-plugin-solid": "^2.11.9", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}