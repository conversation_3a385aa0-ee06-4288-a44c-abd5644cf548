[package]
name = "trailbase-cli"
version = "0.2.0"
edition = "2024"
license = "OSL-3.0"
publish = false

[lib]
doctest = false

[[bin]]
name = "trail"

[features]
default = []
# Conditionally enable "v8" feature of dep:trailbase.
v8 = ["trailbase/v8"]
swagger = ["dep:utoipa-swagger-ui"]
vendor-ssl = ["dep:openssl"]

[dependencies]
axum = { version = "^0.8.1", features=["multipart"] }
chrono = "^0.4.38"
clap = { version = "^4.4.11", features=["derive", "env"] }
env_logger = { workspace = true }
itertools = "0.14.0"
log = "^0.4.21"
mimalloc = { version = "^0.1.41", default-features = false }
minijinja = { workspace = true }
openssl = { version = "0.10.73", features = ["vendored"], optional = true }
reqwest = { workspace = true }
serde = { version = "^1.0.203", features = ["derive"] }
serde_json = "^1.0.117"
tokio = { workspace = true }
trailbase = { workspace = true }
trailbase-build = { workspace = true }
url = "2.5.4"
utoipa = { version = "5.0.0-beta.0", features = ["axum_extras"] }
utoipa-swagger-ui = { version = "9.0.0", features = ["axum"], optional = true }
uuid = { workspace = true }
zip = { version = "5.1.1", default-features = false, features = ["deflate"] }

[build-dependencies]
trailbase-build = { workspace = true }
