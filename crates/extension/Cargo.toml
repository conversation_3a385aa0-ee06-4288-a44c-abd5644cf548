[package]
name = "trailbase-extension"
version = "0.3.0"
edition = "2024"
license = "OSL-3.0"
description = "SQLite extension used by TrailBase"
homepage = "https://trailbase.io"
repository = "https://github.com/trailbaseio/trailbase"
readme = "../README.md"

[lib]
crate-type=["cdylib", "rlib"]

[dependencies]
arc-swap = "1.7.1"
argon2 = { version = "^0.5.3", default-features = false, features = ["alloc", "password-hash", "rand", "std"] }
base64 = { version = "0.22.1", default-features = false }
jsonschema = { version = "0.33.0", default-features = false }
log = "0.4.27"
maxminddb = "0.26.0"
mini-moka = "0.10.3"
parking_lot = { workspace = true }
regex = "1.11.0"
rusqlite = { workspace = true }
serde = { version = "^1.0.203", features = ["derive"] }
serde_json = "1.0.121"
sqlite-vec = { workspace = true }
thiserror = "2.0.12"
trailbase-sqlean = { workspace = true }
uuid = { workspace = true }
validator = { version = "0.20.0", default-features = false }
