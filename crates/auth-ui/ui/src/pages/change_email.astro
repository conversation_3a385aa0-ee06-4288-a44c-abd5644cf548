---
import Form from "@/components/Form.astro";
import Button from "@/components/Button.astro";
import TextFieldInput from "@/components/TextFieldInput.astro";
import TextFieldLabel from "@/components/TextFieldLabel.astro";

import { textFieldInputStyle } from "@/components/ui/text-field";
import { buttonVariants } from "@/components/ui/button";
import { AUTH_API } from "@/lib/constants";

const base = import.meta.env.BASE_URL;
const profilePageUrl = `${base}/profile`;
---

<Form title="Change Email">
  <form
    id="reset-update-form"
    class="flex flex-col gap-2"
    action={`${AUTH_API}/change_email/request`}
    method="post"
    enctype="application/x-www-form-urlencoded"
  >
    <div class="hidden" set:html={`{{ state | escape("none") }}`} />

    <div
      class="my-4 grid grid-cols-2 items-center gap-4"
      style={{ "grid-template-columns": "auto 1fr" }}
    >
      <TextFieldLabel>Old Email:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex="1"
        class={textFieldInputStyle}
        type="email"
        name="old_email"
        placeholder="Email"
      />

      <TextFieldLabel>New Email:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex="2"
        class={textFieldInputStyle}
        type="email"
        name="new_email"
        placeholder="Email"
      />
    </div>

    <div class="flex w-full justify-between">
      <a
        class:list={buttonVariants({ variant: "outline" })}
        href={profilePageUrl}
      >
        Back
      </a>

      <Button tabindex="3" type="submit">Send Email Verification</Button>
    </div>
  </form>
</Form>
