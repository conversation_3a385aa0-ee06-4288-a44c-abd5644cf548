---
import Form from "@/components/Form.astro";
import Button from "@/components/Button.astro";
import TextFieldInput from "@/components/TextFieldInput.astro";
import TextFieldLabel from "@/components/TextFieldLabel.astro";

import { textFieldInputStyle } from "@/components/ui/text-field";
import { buttonVariants } from "@/components/ui/button";
import { AUTH_API } from "@/lib/constants";

const base = import.meta.env.BASE_URL;
const profilePageUrl = `${base}/profile`;
---

<Form title="Change Password">
  <form
    id="reset-update-form"
    class="flex flex-col gap-2"
    action={`${AUTH_API}/change_password`}
    method="post"
    enctype="application/x-www-form-urlencoded"
  >
    <div class="hidden" set:html={`{{ state | escape("none") }}`} />

    <div
      class="my-4 grid grid-cols-2 items-center gap-4"
      style={{ "grid-template-columns": "auto 1fr" }}
    >
      <TextFieldLabel>Old Password:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex={1}
        class={textFieldInputStyle}
        type="password"
        name="old_password"
        placeholder="Old Password"
      />

      <TextFieldLabel>New Password:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex={2}
        class={textFieldInputStyle}
        type="password"
        name="new_password"
        placeholder="New Password"
      />

      <TextFieldLabel>Password Repeat:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex={3}
        class={textFieldInputStyle}
        type="password"
        name="new_password_repeat"
        placeholder="New Password"
      />
    </div>

    <div class="flex w-full justify-between">
      <a
        class:list={buttonVariants({ variant: "outline" })}
        href={profilePageUrl}
      >
        Back
      </a>

      <Button tabindex={4} type="submit">Change</Button>
    </div>
  </form>
</Form>
