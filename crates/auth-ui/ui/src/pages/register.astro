---
import Form from "@/components/Form.astro";
import Button from "@/components/Button.astro";
import TextFieldInput from "@/components/TextFieldInput.astro";
import TextFieldLabel from "@/components/TextFieldLabel.astro";

import { buttonVariants } from "@/components/ui/button";
import { AUTH_API } from "@/lib/constants";
---

<Form title="Register">
  <form
    id="register-form"
    class="flex flex-col gap-2"
    action={`${AUTH_API}/register`}
    method="post"
    enctype="application/x-www-form-urlencoded"
  >
    <div class="hidden" set:html={`{{ state | escape("none") }}`} />

    <div
      class="my-4 grid grid-cols-2 items-center gap-4"
      style={{ "grid-template-columns": "auto 1fr" }}
    >
      <TextFieldLabel>Email:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex="1"
        type="text"
        name="email"
        placeholder="Email"
      />

      <TextFieldLabel>Password:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex="2"
        type="password"
        name="password"
        placeholder="Password"
      />

      <TextFieldLabel>Confirm Password:</TextFieldLabel>
      <TextFieldInput
        required
        tabindex="3"
        type="password"
        name="password_repeat"
        placeholder="Password Confirm"
      />
    </div>

    <div class="flex w-full justify-between">
      <a
        class:list={buttonVariants({ variant: "outline" })}
        href={`${import.meta.env.BASE_URL}/login`}
      >
        Back
      </a>

      <Button tabindex="4" variant="default" type="submit">Register</Button>
    </div>
  </form>
</Form>
