---
import BaseLayout from "@/layouts/BaseLayout.astro";
import Card from "@/components/Card.astro";
import AlertBox from "@/components/AlertBox.astro";

interface Props {
  title: string;
}

const { title } = Astro.props;
---

<BaseLayout title={title}>
  <div class="mx-auto flex h-dvh w-screen flex-col items-center justify-center">
    <Card title={title}>
      <slot />
    </Card>

    {"{% if !alert.is_empty() %}"}
    <AlertBox message={`{{ alert | escape("html") }}`} />
    {"{% endif %}"}
  </div>
</BaseLayout>
