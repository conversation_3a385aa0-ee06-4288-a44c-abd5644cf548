@import "tailwindcss";
@import "tw-animate-css";

@plugin "@kobalte/tailwindcss";

@custom-variant dark (&:where(.dark, .dark *, [data-kb-theme=dark], [data-kb-theme=dark] *));

:root {
  /*
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);

  --info: oklch(0.95 0.024 236.81);
  --info-foreground: oklch(0.68 0.148143 238.1044);
  --success: oklch(0.95 0.0506 162.83);
  --success-foreground: oklch(0.69 0.1481 162.37);
  --warning: oklch(0.96 0.0569 95.61);
  --warning-foreground: oklch(0.71 0.186 48.13);
  --error: oklch(0.93 0.0314 17.73);
  --error-foreground: oklch(0.64 0.2082 25.38);

  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);

  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  */

  --background: hsl(0 0% 100%);
  --foreground: hsl(240 10% 3.9%);

  --muted: hsl(240 4.8% 95.9%);
  --muted-foreground: hsl(240 3.8% 46.1%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(240 10% 3.9%);

  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(240 10% 3.9%);

  --primary: hsl(199 100% 33%);
  /* --primary: 240 5.9% 10%; */
  --primary-foreground: hsl(0 0% 98%);

  --secondary: hsl(240 4.8% 95.9%);
  --secondary-foreground: hsl(240 5.9% 10%);

  --accent: hsl(240 4.8% 95.9%);
  --accent-foreground: hsl(240 5.9% 10%);

  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);

  --info: hsl(204 94% 94%);
  --info-foreground: hsl(199 89% 48%);

  --success: hsl(149 80% 90%);
  --success-foreground: hsl(160 84% 39%);

  --warning: hsl(48 96% 89%);
  --warning-foreground: hsl(25 95% 53%);

  --error: hsl(0 93% 94%);
  --error-foreground: hsl(0 84% 60%);

  --ring: hsl(240 5.9% 10%);
}

.dark,
[data-kb-theme="dark"] {
  /*
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);

  --info: oklch(0.28 0.0875 267.91);
  --info-foreground: oklch(0.68 0.148143 238.1044);
  --success: oklch(0.26 0.0487 172.54);
  --success-foreground: oklch(0.69 0.1481 162.37);
  --warning: oklch(0.29 0.0638 53.82);
  --warning-foreground: oklch(0.71 0.186 48.13);
  --error: oklch(0.26 0.0886 26.05);
  --error-foreground: oklch(0.64 0.2082 25.38);

  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);

  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  */

  --background: hsl(240 10% 3.9%);
  --foreground: hsl(0 0% 98%);

  --muted: hsl(240 3.7% 15.9%);
  --muted-foreground: hsl(240 5% 64.9%);

  --accent: hsl(240 3.7% 15.9%);
  --accent-foreground: hsl(0 0% 98%);

  --popover: hsl(240 10% 3.9%);
  --popover-foreground: hsl(0 0% 98%);

  --border: hsl(240 3.7% 15.9%);
  --input: hsl(240 3.7% 15.9%);

  --card: hsl(240 10% 3.9%);
  --card-foreground: hsl(0 0% 98%);

  --primary: hsl(0 0% 98%);
  --primary-foreground: hsl(240 5.9% 10%);

  --secondary: hsl(240 3.7% 15.9%);
  --secondary-foreground: hsl(0 0% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);

  --info: hsl(204 94% 94%);
  --info-foreground: hsl(199 89% 48%);

  --success: hsl(149 80% 90%);
  --success-foreground: hsl(160 84% 39%);

  --warning: hsl(48 96% 89%);
  --warning-foreground: hsl(25 95% 53%);

  --error: hsl(0 93% 94%);
  --error-foreground: hsl(0 84% 60%);

  --ring: hsl(240 4.9% 83.9%);
}

@theme {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-error: var(--error);
  --color-error-foreground: var(--error-foreground);

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /*
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  */

  --radius: 0.5rem;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--kb-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--kb-accordion-content-height);
    }

    to {
      height: 0;
    }
  }

  @keyframes content-show {
    from {
      opacity: 0;
      transform: scale(0.96);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes content-hide {
    from {
      opacity: 1;
      transform: scale(1);
    }

    to {
      opacity: 0;
      transform: scale(0.96);
    }
  }
}

@utility container {
  @apply px-8;

  @media (width >=--theme(--breakpoint-sm)) {
    @apply max-w-none;
  }

  @media (max-width: 640px) {
    @apply px-4;
  }
}

@utility step {
  counter-increment: step;

  &:before {
    @apply bg-muted border-background absolute inline-flex h-9 w-9 items-center justify-center rounded-full border-4 text-center -indent-px font-mono text-base font-medium;
    @apply mt-[-4px] ml-[-50px];
    content: counter(step);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }

  /**
   * Scrollbar
   */

  ::-webkit-scrollbar {
    width: 16px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 9999px;
    border: 4px solid transparent;
    background-clip: content-box;
    @apply bg-accent;
  }

  ::-webkit-scrollbar-corner {
    display: none;
  }
}

/* Customization below. Above from https://github.com/stefan-karger/solid-ui/issues/166#issuecomment-2819024081 */

@theme {
  /* Generated accent color palettes for "Ocean AA". */
  --color-accent-200: #92d1fe;
  --color-accent-600: #0073aa;
  --color-accent-900: #003653;
  --color-accent-950: #00273d;
  /* Generated gray color palettes "Ocean AA". */
  --color-gray-100: #f3f7f9;
  --color-gray-200: #e7eff2;
  --color-gray-300: #bac4c8;
  --color-gray-400: #7b8f96;
  --color-gray-500: #495c62;
  --color-gray-700: #2a3b41;
  --color-gray-800: #182a2f;
  --color-gray-900: #121a1c;
}

@layer base {
  h1 {
    @apply text-2xl;
    @apply mb-2;
    font-weight: 700;
  }

  h2 {
    @apply text-xl;
    @apply mb-2;
    font-weight: 700;
  }

  h3 {
    font-weight: 700;
  }

  body {
    font-family: "Inter", sans-serif;
  }
}

@media (max-width: 640px) {
  .container {
    @apply px-4;
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbars::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbars {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
