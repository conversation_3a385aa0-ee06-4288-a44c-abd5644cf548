{"name": "trailbase-auth-ui", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "check": "astro check && eslint", "format": "prettier -w ."}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/solid-js": "^5.1.1", "@kobalte/core": "^0.13.11", "@nanostores/solid": "^1.1.1", "astro": "^5.14.1", "astro-icon": "^1.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "nanostores": "^1.0.1", "solid-icons": "^1.1.0", "solid-js": "^1.9.9", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "trailbase": "file:../../assets/js/client"}, "devDependencies": {"@eslint/js": "^9.37.0", "@iconify-json/tabler": "^1.2.23", "@kobalte/tailwindcss": "^0.9.0", "@tailwindcss/typography": "^0.5.19", "@tailwindcss/vite": "^4.1.14", "eslint": "^9.37.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-better-tailwindcss": "^3.7.9", "eslint-plugin-solid": "^0.14.5", "globals": "^16.4.0", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.14", "sharp": "^0.34.4", "tailwindcss": "^4.1.14", "tw-animate-css": "^1.4.0", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0"}}