{"name": "record_api_ts", "version": "1.0.0", "description": "Example uses of record APIs for documentation purposes.", "scripts": {"check": "tsc --noEmit --skipLibCheck && eslint", "manual-test": "vitest run # Not hermetic, don't run @CI"}, "devDependencies": {"@eslint/js": "^9.37.0", "@types/node": "^24.7.0", "eslint": "^9.37.0", "prettier": "^3.6.2", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vitest": "^3.2.4"}, "dependencies": {"trailbase": "workspace:*"}}