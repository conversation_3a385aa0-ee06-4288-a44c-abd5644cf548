---
title: Welcome to TrailBase
description: >
  A blazingly fast, open-source, single-executable, featureful Firebase alternative.
template: splash
hero:
  image:
    file: ../../assets/logo_512.webp
  actions:
    - text: Documentation
      link: /getting-started/install
      icon: open-book
    - text: FAQ
      link: /reference/faq/
      icon: external
      variant: secondary
    - text: GitHub
      link: https://github.com/trailbaseio/trailbase
      icon: github
      variant: secondary
    - text: Demo
      link: https://demo.trailbase.io
      icon: rocket
      variant: secondary
---

import { Image } from "astro:assets";
import { Aside, Card, CardGrid } from "@astrojs/starlight/components";

import SplitCard from "@/components/SplitCard.astro";
import Footer from "@/components/layout/Footer.astro";

import shelve from "@/assets/shelve.webp";

import dotnetLogo from "@/assets/dotnet_logo.svg";
import flutterLogo from "@/assets/flutter_logo.svg";
import pythonLogo from "@/assets/python_logo.svg";
import rustLogo from "@/assets/rust_unofficial_logo.svg";
import goLogo from "@/assets/go_logo.svg";
import swiftLogo from "@/assets/swift_logo.svg";
import tsLogo from "@/assets/ts_logo.svg";
import kotlinLogo from "@/assets/kotlin_logo.svg";
import tanstackLogo from "@/assets/tanstack_logo.svg";

import { Duration100kInsertsChart } from "./reference/_benchmarks/benchmarks.tsx";
import { githubPath } from "@/lib/github";

export const demoLink = "https://demo.trailbase.io";

<div class="flex flex-col gap-4">

  <SplitCard title="Performance" icon="rocket">
    <div slot="first">
      Blazingly fast thanks to its constituents:

      * Rust: one of the lowest overhead languages,
      * Axum: one of the fastest HTTP servers,
      * SQLite: one of the fastest full-SQL databases,
      * Wasmtime: compiles custom endpoints to efficient native code.

      TrailBase's APIs are [11x faster than PocketBase's and almost 40x faster than SupaBase's
      with a fraction of the footprint](/reference/benchmarks) allowing you to
      serve millions of customers from a tiny box.
    </div>

    <div slot="second">
      <a href="/reference/benchmarks">Total time for 100k insertions:</a>

      <div class="w-full h-[300px]">
        <Duration100kInsertsChart client:only="solid-js" />
      </div>
    </div>
  </SplitCard>

  <Card title="Admin Dashboard" icon="setting">
    TrailBase ships with a builtin admin dashboard UI that lets you quickly
    configure your instance and visually explore your data.
    Check out the **live demo** below.
    Following TrailBase's mantra of not getting in your way, the UI is
    entirely optional letting you fall back to a purely config and
    migration-based setup for integration tests or managing a fleet of
    deployments.

    <div class="flex justify-center">
      <div class="max-w-[680px] relative" >
        <a href={demoLink}>
          <Image class="z-0 rounded-xl" src={shelve} alt="Screenshots of TrailBase's admin dashboard" />
        </a>

        <div class="z-1 w-full h-full absolute top-0 flex justify-center items-center pointer-events-none">
          <a
            class="pointer-events-auto no-underline flex flex-col items-center bg-gray-200 dark:bg-accent-900 px-4 py-2 rounded"
            href={demoLink}
          >
            <strong>Live Demo</strong>
            <span>login: admin@localhost</span>
            <span>password: secret</span>
          </a>
        </div>
      </div>
    </div>
  </Card>

  <Card title="Integrations" icon="puzzle">
    Straightforward integration with any stack thanks to thin abstractions,
    reliance on standards, and JSON Schema for type-safety allowing type-safe
    bindings for virtually any language.

    Clients as well as code-generation examples for TypeScript,
    Dart/Flutter, Python, C#/.NET and Rust are provided out of the box.

    <div class="pt-4 m-0 gap-4 grid grid-cols-8 justify-center items-start">
      <a href="https://www.npmjs.com/package/trailbase">
        <Image class="p-0 m-0" width={52} height={52} src={tsLogo} alt="TypeScript" />
      </a>

      <a href="https://pub.dev/packages/trailbase">
        <Image margin={0} class="p-0 m-0" width={52} height={52} src={flutterLogo} alt="Flutter" />
      </a>

      <a href={githubPath("client/swift/trailbase")}>
        <Image margin={0} class="p-0 m-0" width={52} height={52} src={swiftLogo} alt="Swift" />
      </a>

      <a href={githubPath("client/kotlin")}>
        <Image margin={0} class="p-0 m-0" width={52} height={52} src={kotlinLogo} alt="Kotlin" />
      </a>

      <a href="https://pypi.org/project/trailbase/">
        <Image margin={0} class="p-0 m-0" width={52} height={52} src={pythonLogo} alt="Python" />
      </a>

      <a href="https://www.nuget.org/packages/TrailBase/">
        <Image margin={0} class="p-0 m-0" width={52} height={52} src={dotnetLogo} alt="Dotnet" />
      </a>

      <a href="https://crates.io/crates/trailbase-client">
        <Image margin={0} class="p-0 m-0" width={52} height={52} src={rustLogo} alt="Rust" />
      </a>

      <a href={githubPath("client/go/trailbase")}>
        <Image margin={0} class="p-0 m-0" width={52} height={52} src={goLogo} alt="Go" />
      </a>
    </div>
  </Card>

  <CardGrid>

    <Card title="Realtime Sync" icon="cloud-download">
      Subscribe to push-based realtime data changes letting you act quickly or
      synchronize your clients.

      [TanStack/db](https://tanstack.com/db/latest) ships with a TrailBase
      integration out-of-the-box, check out
      <a href={githubPath("examples/tanstack-db-sync")}>example</a>.

      <div class="flex justify-center items-center">
        <a href="https://github.com/TanStack/db/tree/main/packages/trailbase-db-collection">
          <Image class="p-0 m-0" height={100} src={tanstackLogo} alt="TanStack/db" />
        </a>
      </div>
    </Card>

    <Card title="Simple & Consistent" icon="heart">
      TrailBase is a single executable that is incredibly
      easy to deploy **consistently** across integration testing, development,
      pre-prod, and production environments including edge.
      Architecturally, TrailBase aims to be a simple, thin abstraction around
      standards helping full or piece-meal adoption and avoiding lock-in.

      A simple architecture for your App and also your dependencies lets you
      move faster, more confidently and pivot when necessary.
    </Card>

    <Card title="Built-in Auth" icon="open-book">
      TrailBase comes with an auth system and UI built in supporting
      both password-based and Social/OAuth (Google, Discord, ...) sign-ups.

      TrailBase's auth system follows standards and best-practices
      combining short-lived, stateless JSON web tokens with long-lived stateful
      refresh tokens letting you easily and efficiently authenticate your users
      from any of your other back-ends relying on safe, asymmetric cryptography.
    </Card>

    <Card title="APIs & File Storage" icon="random">
      Provide access to your tables and views through fast, flexible and
      **type-safe** restful CRUD APIs.
      Listen for data changes with realtime APIs and extend functionality using
      a fast WebAssembly runtime with support for many guest languages.

      Authorize users based on ACLs and SQL access rules letting you
      easily build higher-level access management or moderation facilities
      like groups or capabilities.
    </Card>

  </CardGrid>

<div class="h-12" />

</div>
