{"name": "trailbase-website", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "check": "astro check && eslint", "format": "prettier -w ."}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/rss": "^4.0.12", "@astrojs/starlight": "^0.36.0", "@astrojs/starlight-tailwind": "^4.0.1", "@iconify-json/tabler": "^1.2.23", "@tailwindcss/vite": "^4.1.14", "astro": "^5.14.1", "astro-icon": "^1.1.5", "chart.js": "^4.5.0", "chartjs-chart-error-bars": "^4.4.5", "chartjs-plugin-deferred": "^2.0.0", "clsx": "^2.1.1", "sharp": "^0.34.4", "solid-js": "^1.9.9", "starlight-links-validator": "^0.18.0", "starlight-openapi": "^0.20.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.14", "typescript": "^5.9.3"}, "devDependencies": {"@astrojs/sitemap": "^3.6.0", "@astrojs/solid-js": "^5.1.1", "@eslint/js": "^9.37.0", "astro-robots-txt": "^1.0.0", "eslint": "^9.37.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-better-tailwindcss": "^3.7.9", "eslint-plugin-solid": "^0.14.5", "globals": "^16.4.0", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.14", "typescript-eslint": "^8.46.0"}}