[workspace]
resolver = "2"
members = [
  "client/testfixture/guests/rust",
  "crates/assets",
  "crates/auth-ui",
  "crates/build",
  "crates/cli",
  "crates/client",
  "crates/core",
  "crates/extension",
  "crates/js-runtime",
  "crates/qs",
  "crates/refinery",
  "crates/schema",
  "crates/sqlean",
  "crates/sqlite",
  "crates/wasi-keyvalue",
  "crates/wasm-runtime-host",
  "crates/wasm-runtime-common",
  "crates/wasm-runtime-guest",
  "docs/examples/record_api_rs",
  "examples/custom-binary",
  "examples/wasm-guest-rust",
  "examples/coffee-vector-search/guests/rust",
  "examples/collab-clicker-ssr/guests/rust",
  "vendor/sqlite-vec/bindings/rust",
]
default-members = [
  "crates/assets",
  "crates/build",
  "crates/cli",
  "crates/client",
  "crates/core",
  "crates/extension",
  "crates/js-runtime",
  "crates/qs",
  "crates/refinery",
  "crates/schema",
  "crates/sqlean",
  "crates/sqlite",
  "crates/wasi-keyvalue",
  "crates/wasm-runtime-host",
  "crates/wasm-runtime-common",
  "crates/wasm-runtime-guest",
]

# https://doc.rust-lang.org/cargo/reference/profiles.html
[profile.release]
panic = "unwind"
opt-level = 3
# LTO is fun. A few observations:
# * PGO doesn't work with LTO: https://github.com/llvm/llvm-project/issues/57501
# * Cranelift doesn't work with LTO: missing symbols during linking.
# * Setting `lto` to true or "fat" has a significant impact on build times
#   manifesting as `LLVM_passes` and `finish_ongoing_codegen` in the build
#   timings. Also increasing the `codegen-units` doesn't seem to have an effect
#   in "fat"-mode.
# * In TB benchmarks "fat" did not perform better than "thin" LTO.
# * However, both "fat" LTO and "codegen-units = 1" reduced binary size. We're
#   still using it in github releases.
lto = "thin" # ("off", "thin", "fat")
codegen-units = 16
strip = "debuginfo" # ("symbols", "debuginfo", "none")

# Workaround for https://github.com/gwenn/lemon-rs/issues/78. sqlite3-parser
# requires 1+MB stack frames to parse trivial SQL statements, which is larger
# than Window's default stack size of 1MB. This is due to the rust compiler not
# overlapping stack variables of disjoint branches in dev mode and instead
# allocating them all.
[profile.dev.package.sqlite3-parser]
opt-level = 1

[workspace.dependencies]
askama = { version = "0.14.0", default-features = false, features = ["derive", "std", "config"] }
axum = { version = "^0.8.1", features = ["multipart"] }
env_logger = { version = "^0.11.8", default-features = false, features = ["auto-color", "humantime"] }
libsqlite3-sys = { version = "0.35.0", default-features = false, features = ["bundled", "preupdate_hook"] }
minijinja = { version = "2.1.2", default-features = false }
parking_lot = { version = "0.12.3", default-features = false, features = ["send_guard", "arc_lock"] }
rand = { version = "^0.9.0" }
reqwest = { version = "0.12.8", default-features = false, features = ["rustls-tls", "json"] }
rusqlite = { version = "0.37.0", default-features = false, features = ["bundled", "column_decltype", "functions", "backup", "preupdate_hook"] }
rust-embed = { version = "8.4.0", default-features = false, features = ["mime-guess"] }
sqlite-vec = { path = "vendor/sqlite-vec/bindings/rust", default-features = false }
tokio = { version = "^1.38.0", default-features = false, features = ["macros", "net", "rt-multi-thread", "fs", "signal", "time", "sync"] }
tracing = { version = "0.1.40", default-features = false }
tracing-subscriber = { version = "0.3.18", default-features = false, features = ["smallvec", "std", "fmt", "json"] }
trailbase = { path = "crates/core", version = "0.2.0", default-features = false, features=["wasm"] }
trailbase-assets = { path = "crates/assets", version = "0.2.0" }
trailbase-build = { path = "crates/build", version = "0.1.1" }
trailbase-client = { path = "crates/client", version = "0.5.0" }
trailbase-extension = { path = "crates/extension", version = "0.3.0" }
trailbase-js = { path = "crates/js-runtime", version = "0.2.0" }
trailbase-qs = { path = "crates/qs", version = "0.1.0" }
trailbase-refinery = { path = "crates/refinery", version = "0.1.0" }
trailbase-schema = { path = "crates/schema", version = "0.1.0" }
trailbase-sqlean = { path = "crates/sqlean", version = "0.0.3" }
trailbase-sqlite = { path = "crates/sqlite", version = "0.3.0" }
trailbase-wasi-keyvalue = { path = "crates/wasi-keyvalue", version = "0.1.0" }
trailbase-wasm = { path = "crates/wasm-runtime-guest", version = "0.3.0" }
trailbase-wasm-common = { path = "crates/wasm-runtime-common", version = "0.1.0" }
trailbase-wasm-runtime-host = { path = "crates/wasm-runtime-host", version = "0.1.0" }
ts-rs = { version = "11", features = ["uuid-impl", "serde-json-impl"] }
uuid = { version = "1", default-features = false, features = ["std", "v4", "v7", "serde"] }
wasmtime = "37.0.0"
wasmtime-wasi = { version = "37.0.0", default-features = false, features = [] }
wasmtime-wasi-http = "37.0.0"
wasmtime-wasi-io = "37.0.0"
